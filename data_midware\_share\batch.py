from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from sqlalchemy.orm import Session
    from uuid import UUID

from aiocache import cached
from os import getenv, urandom
from time import time
from asyncio import sleep, create_task

from .._main.table import TB_USERS_SESSION
from .._main.batch import get_system_config, match_ip, get_hash
from .sql import (
    get_all_users,
    get_all_user_ids_in_session,
    simple_select_one,
    select_multi,
    simple_update_one,
    delete_multi
)
from .misc import CachedEmptyResult, session_nonce_store
from .error import ErrorCode

try:
    DATA_CLEAR_INTERVAL = int(getenv('DATA_CLEAR_INTERVAL', 3600))
except Exception:
    DATA_CLEAR_INTERVAL = 3600
try:
    SESSION_TOKEN_UPDATE_INTERVAL = int(getenv('SESSION_TOKEN_UPDATE_INTERVAL', 600))
except Exception:
    SESSION_TOKEN_UPDATE_INTERVAL = 600
try:
    SESSION_NONCE_STORE_CLEAR_TIMEOUT = int(getenv('SESSION_NONCE_STORE_CLEAR_TIMEOUT', 10))
except Exception:
    SESSION_NONCE_STORE_CLEAR_TIMEOUT = 10


@cached(
    ttl=SESSION_TOKEN_UPDATE_INTERVAL,
    key_builder=lambda _, *args, **kwargs: args[1] if args else kwargs['session_id']
)
async def update_user_session(
    session: 'Session',
    session_id: str,
    session_tokens: list[str],
    ip: str,
    time_: float = time()
) -> tuple[bytes, float]:
    system_config = await get_system_config(session)
    new_session_token = urandom(64)
    session_tokens.insert(0, get_hash(new_session_token, algorithm='md5'))
    while len(session_tokens) > max(system_config.user_session_token_fallback + 1, 1):
        session_tokens.pop()
    await simple_update_one(
        session,
        TB_USERS_SESSION,
        {'session_id': session_id},
        {'session_token': session_tokens, 'ip': ip, 'refresh_time': time_}
    )
    return new_session_token, time_


async def get_user_info(
    session: 'Session',
    ip: str,
    session_id: 'UUID',
    session_token: bytes,
    time_: float = time()
) -> dict:
    system_config = await get_system_config(session)
    await delete_expired_users_session(session, time_)
    await delete_redundant_users_session(session)
    _session = await simple_select_one(session, TB_USERS_SESSION, {'session_id': session_id})
    if not _session:
        return {'error': ErrorCode.SESSION_NOT_FOUND}
    limit_sessions = await select_multi(
        session,
        TB_USERS_SESSION,
        ['session_id'],
        in_={'user_id': [_session.user_id]},
        order={'id': True},
        limit=system_config.user_session_number
    )
    if session_id not in [r['session_id'] for r in limit_sessions]:
        return {'user_id': _session.user_id, 'error': ErrorCode.SESSION_REPLACED}
    if get_hash(session_token, algorithm='md5') not in _session.session_token:
        return {'user_id': _session.user_id, 'error': ErrorCode.SESSION_TOKEN_WRONG}
    if time_ - _session.refresh_time > system_config.user_session_expire * 60:
        return {'user_id': _session.user_id, 'error': ErrorCode.SESSION_EXPIRED}
    user = await get_all_users(session, user_id=[_session.user_id])
    if ip != _session.ip:
        _match = match_ip(
            ip,
            [user[0]['allow_ip'], system_config.system_allow_ip],
            [user[0]['deny_ip'], system_config.system_deny_ip]
        )
        if not _match:
            return {'user_id': _session.user_id, 'error': ErrorCode.IP_FORBIDDEN}
    return {
        'user_id': _session.user_id,
        'session_id': _session.session_id,
        'session_tokens': _session.session_token,
        'user_info': user[0]
    }


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda *_, **__: ''
)
async def delete_expired_users_session(
    session: 'Session',
    time_: float = time()
) -> CachedEmptyResult:
    system_config = await get_system_config(session)
    await delete_multi(
        session,
        TB_USERS_SESSION,
        lt={'refresh_time': time_ - system_config.user_session_expire * 60}
    )
    return CachedEmptyResult(0)


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda *_, **__: ''
)
async def delete_redundant_users_session(session: 'Session') -> CachedEmptyResult:
    system_config = await get_system_config(session)
    u_ids = await get_all_user_ids_in_session(session)
    s_ids = []
    for user_id in u_ids:
        results = await select_multi(
            session,
            TB_USERS_SESSION,
            ['session_id'],
            in_={'user_id': [user_id]},
            order={'id': True},
            offset=system_config.user_session_number
        )
        s_ids.extend(results)
    if len(s_ids) == 0:
        return CachedEmptyResult(2)
    await delete_multi(session, TB_USERS_SESSION, in_={'session_id': [r['session_id'] for r in s_ids]})
    return CachedEmptyResult(0)


async def check_session_nonce(
    session_id: 'UUID',
    nonce: int
) -> int:
    if session_id not in session_nonce_store:
        return 2
    if nonce in session_nonce_store[session_id]:
        return 0
    if nonce < min(session_nonce_store[session_id]):
        return 0
    if nonce >= 18446744073709551615:
        session_nonce_store.pop(session_id)
    else:
        session_nonce_store[session_id].append(nonce)
    create_task(delete_old_nonce(session_id, nonce))
    return 1


async def delete_old_nonce(
    session_id: 'UUID',
    nonce: int
) -> None:
    await sleep(SESSION_NONCE_STORE_CLEAR_TIMEOUT)
    session_nonce_store[session_id] = [x for x in session_nonce_store[session_id] if x >= nonce]


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda *_, **__: ''
)
async def delete_redundant_session_nonce(session: 'Session') -> CachedEmptyResult:
    results = await select_multi(session, TB_USERS_SESSION, ['session_id'])
    present_sessions = [row['session_id'] for row in results]
    for session_id in list(session_nonce_store.keys()):
        if session_id not in present_sessions:
            session_nonce_store.pop(session_id)
    return CachedEmptyResult(0)
