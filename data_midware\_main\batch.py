from typing import TYPE_CHECKING, Union
if TYPE_CHECKING:
    from sqlalchemy.orm import Session
    from sqlalchemy.engine import Row
    from uuid import UUID

from ipaddress import ip_network, ip_address, IPv4Network, IPv6Network
from hashlib import new
from base64 import b64encode
from re import escape, search
from string import ascii_lowercase, ascii_uppercase, digits, punctuation, printable, whitespace
from random import choice
from time import time

from aiocache import cached
from os import getenv, urandom

from .table import (
    TB_USERS_MAIN,
    TB_USERS_OTHER,
    TB_USERS_LOCK,
    TB_USERS_HISTORY_LOGIN,
    TB_USERS_HISTORY_PASSWD,
    TB_USERS_HISTORY_CONFIG,
    TB_USERS_HISTORY_DELETE,
    TB_USERS_LOGIN_FAIL,
    TB_SYSTEM_CONFIG
)
from .param import HistoryConfig
from .._share.sql import simple_select_one, select_multi, simple_insert_one, delete_multi
from .._share.misc import CachedEmptyResult
from .._share.crypto import Crypto_AES

try:
    DATA_CLEAR_INTERVAL = int(getenv('DATA_CLEAR_INTERVAL', 3600))
except Exception:
    DATA_CLEAR_INTERVAL = 3600


@cached(key_builder=lambda *_, **__: '')
async def get_system_config(
    session: 'Session'
) -> Union['Row', None]:
    return await simple_select_one(session, TB_SYSTEM_CONFIG, {'id': 0})


def check_cidr(cidr: str) -> IPv4Network | IPv6Network | None:
    try:
        return ip_network(cidr, strict=False)
    except Exception:
        return None


def match_ip(ip: str, allow: list[list[str]], deny: list[list[str]]) -> bool:
    try:
        _ip = ip_address(ip)
    except Exception:
        return False
    for _allow in allow:
        found = False
        for _cidr in _allow:
            if _ip in check_cidr(_cidr):
                found = True
                break
        if not found:
            return False
    for _deny in deny:
        for _cidr in _deny:
            if _ip in check_cidr(_cidr):
                return False
    return True


def get_hash(data: bytes, salt: bytes = b'', algorithm: str = 'shake_256') -> str:
    hasher = new(algorithm)
    hasher.update(data)
    if salt:
        hasher.update(salt)
    if algorithm == 'shake_256':
        result = hasher.digest(64)
    elif algorithm == 'shake_128':
        result = hasher.digest(32)
    else:
        result = hasher.digest()
    return b64encode(result).decode()


async def check_password_compliance(
    session: 'Session',
    password: str
) -> bool:
    system_config = await get_system_config(session)
    if system_config.user_passwd_require_digit:
        if not search(rf'[{digits}]', password):
            return False
    if system_config.user_passwd_require_upper:
        if not search(rf'[{ascii_uppercase}]', password):
            return False
    if system_config.user_passwd_require_lower:
        if not search(rf'[{ascii_lowercase}]', password):
            return False
    if system_config.user_passwd_require_special:
        if not search(rf'[{escape(punctuation + whitespace)}]', password):
            return False
    if system_config.user_passwd_require_unicode:
        if not search(rf'[^{escape(printable)}]', password):
            return False
    if len(password) < system_config.user_passwd_require_length:
        return False
    return True


def generate_random_string(
    length: int = 4,
    digit: bool = True,
    upper: bool = True,
    lower: bool = True,
    special: bool = False
) -> str:
    _dict = ''
    if digit:
        _dict += digits
    if upper:
        _dict += ascii_uppercase
    if lower:
        _dict += ascii_lowercase
    if special:
        _dict += punctuation
    if not _dict:
        return ''
    return ''.join(choice(_dict) for _ in range(length))


async def add_user(
    session: 'Session',
    email: bytes,
    password: bytes,
    admin: bool = False,
    email2: bytes = b'',
    name: str = f'user_{generate_random_string()}',
    allow_ip: list[str] = ['0.0.0.0/0', '::/0'],
    deny_ip: list[str] = [],
    protect: bool = False,
    time_: float = time(),
    ip: str = 'system'
) -> None:
    email_mask = email.decode()[:3]
    password_salt = urandom(32)
    aes = Crypto_AES(email)
    user_id, = await simple_insert_one(
        session,
        TB_USERS_MAIN,
        {
            'email': get_hash(email),
            'email_mask': email_mask,
            'password': get_hash(password, password_salt),
            'password_salt': password_salt,
            'data_key': aes.encrypt(urandom(256)),
            'admin': admin,
            'status': 0
        },
        returning=['user_id']
    )
    email2_mask = email2.decode()[:3]
    await simple_insert_one(
        session,
        TB_USERS_OTHER,
        {
            'user_id': user_id,
            'email2': get_hash(email2) if email2 else '',
            'email2_mask': email2_mask,
            'name': name,
            'allow_ip': allow_ip,
            'deny_ip': deny_ip,
            'protect': protect
        }
    )
    await insert_history_config(
        session,
        user_id,
        time_,
        ip,
        email_mask=email_mask,
        password=0,
        email2_mask=email2_mask,
        name=name,
        allow_ip=allow_ip,
        deny_ip=deny_ip,
        protect=protect
    )
    await insert_history_passwd(
        session,
        user_id,
        get_hash(password, password_salt),
        time_
    )


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda _, *args, **kwargs: args[1] if args else kwargs['user_id']
)
async def delete_old_users_history_login(
    session: 'Session',
    user_id: 'UUID'
) -> CachedEmptyResult:
    system_config = await get_system_config(session)
    if system_config.user_history_login_number <= 0:
        return CachedEmptyResult(2)
    results = await select_multi(
        session,
        TB_USERS_HISTORY_LOGIN,
        ['id'],
        in_={'user_id': [user_id]},
        order={'id': True},
        offset=system_config.user_history_login_number
    )
    await delete_multi(
        session,
        TB_USERS_HISTORY_LOGIN,
        in_={'id': [r['id'] for r in results]}
    )
    return CachedEmptyResult(0)


async def insert_history_login(
    session: 'Session',
    user_id: 'UUID',
    time_: float,
    ip: str,
    result: int
) -> None:
    system_config = await get_system_config(session)
    if system_config.user_history_login_number != 0:
        await simple_insert_one(
            session,
            TB_USERS_HISTORY_LOGIN,
            {'user_id': user_id, 'time': time_, 'ip': ip, 'result': result}
        )
    await delete_old_users_history_login(session, user_id)


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda _, *args, **kwargs: args[1] if args else kwargs['user_id']
)
async def delete_old_users_history_passwd(
    session: 'Session',
    user_id: 'UUID'
) -> CachedEmptyResult:
    system_config = await get_system_config(session)
    if system_config.user_history_passwd_number <= 0:
        return CachedEmptyResult(2)
    results = await select_multi(
        session,
        TB_USERS_HISTORY_PASSWD,
        ['id'],
        in_={'user_id': [user_id]},
        order={'id': True},
        offset=system_config.user_history_passwd_number
    )
    await delete_multi(
        session,
        TB_USERS_HISTORY_PASSWD,
        in_={'id': [r['id'] for r in results]}
    )
    return CachedEmptyResult(0)


async def insert_history_passwd(
    session: 'Session',
    user_id: 'UUID',
    password_hash: str,
    time_: float
) -> None:
    system_config = await get_system_config(session)
    if system_config.user_history_passwd_number != 0:
        await simple_insert_one(
            session,
            TB_USERS_HISTORY_PASSWD,
            {'user_id': user_id, 'password': password_hash, 'time': time_}
        )
    await delete_old_users_history_passwd(session, user_id)


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda _, *args, **kwargs: args[1] if args else kwargs['user_id']
)
async def delete_old_users_history_config(
    session: 'Session',
    user_id: 'UUID'
) -> CachedEmptyResult:
    system_config = await get_system_config(session)
    if system_config.user_history_config_number <= 0:
        return CachedEmptyResult(2)
    results = await select_multi(
        session,
        TB_USERS_HISTORY_CONFIG,
        ['id'],
        in_={'user_id': [user_id]},
        order={'id': True},
        offset=system_config.user_history_config_number
    )
    await delete_multi(
        session,
        TB_USERS_HISTORY_CONFIG,
        in_={'id': [r['id'] for r in results[:-1]]}
    )
    return CachedEmptyResult(0)


async def insert_history_config(
    session: 'Session',
    user_id: 'UUID',
    time_: float,
    ip: str,
    **value: HistoryConfig
) -> None:
    system_config = await get_system_config(session)
    if system_config.user_history_config_number != 0:
        await simple_insert_one(
            session,
            TB_USERS_HISTORY_CONFIG,
            {'user_id': user_id, 'time': time_, 'ip': ip, **value}
        )
    await delete_old_users_history_config(session, user_id)


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda *_, **__: ''
)
async def delete_old_users_history_delete(
    session: 'Session',
) -> CachedEmptyResult:
    system_config = await get_system_config(session)
    if system_config.user_history_delete_number <= 0:
        return CachedEmptyResult(2)
    results = await select_multi(
        session,
        TB_USERS_HISTORY_DELETE,
        ['id'],
        order={'id': True},
        offset=system_config.user_history_delete_number
    )
    await delete_multi(
        session,
        TB_USERS_HISTORY_DELETE,
        in_={'id': [r['id'] for r in results]}
    )
    return CachedEmptyResult(0)


async def insert_history_delete(
    session: 'Session',
    email: str,
    time_: float,
    ip: str
) -> None:
    system_config = await get_system_config(session)
    if system_config.user_history_delete_number != 0:
        await simple_insert_one(
            session,
            TB_USERS_HISTORY_DELETE,
            {'email': email, 'time': time_, 'ip': ip}
        )
    await delete_old_users_history_delete(session)


@cached(
    ttl=DATA_CLEAR_INTERVAL,
    key_builder=lambda *_, **__: ''
)
async def delete_expired_users_lock(
    session: 'Session',
    time_: float
) -> CachedEmptyResult:
    system_config = await get_system_config(session)
    if system_config.user_lock_period >= 0:
        await delete_multi(
            session,
            TB_USERS_LOCK,
            lt={'time': time_ - system_config.user_lock_period * 60}
        )
    await delete_multi(
        session,
        TB_USERS_LOGIN_FAIL,
        lt={'time': time_ - system_config.login_fail_count_reset_period * 60}
    )
    return CachedEmptyResult(0)
