from cryptography.hazmat.primitives.serialization import Encoding, PublicFormat
from cryptography.hazmat.primitives.hashes import SHA256
from cryptography.hazmat.primitives.padding import PKCS7
from cryptography.hazmat.primitives.kdf.concatkdf import Concat<PERSON><PERSON>Hash
from cryptography.hazmat.primitives.ciphers import Cipher
from cryptography.hazmat.primitives.ciphers.algorithms import AES
from cryptography.hazmat.primitives.ciphers.modes import CBC
from cryptography.hazmat.primitives.asymmetric.rsa import generate_private_key
from cryptography.hazmat.primitives.asymmetric.padding import MGF1, OAEP
from base64 import b64encode, b64decode


class Crypto_AES(object):
    def __init__(self, password: bytes = b'') -> None:
        _kdf = ConcatKDFHash(
            algorithm=SHA256(),
            length=64,  # 输出48字节也是循环2次
            otherinfo=None
        )
        _keyiv = _kdf.derive(password)
        self.key = _keyiv[-32:]  # AES最大支持32字节
        self.iv = _keyiv[:16]  # CBC模式需要和AES.block_size一致，即16字节
        self.cipher = Cipher(AES(self.key), CBC(self.iv))
        self.padding = PKCS7(AES.block_size)

    def encrypt(self, plainbytes: bytes) -> str:
        _encryptor = self.cipher.encryptor()
        _padder = self.padding.padder()
        _paddedbytes = _padder.update(plainbytes) + _padder.finalize()
        _cipherbytes = _encryptor.update(_paddedbytes) + _encryptor.finalize()
        return b64encode(_cipherbytes).decode()

    def decrypt(self, cipherb64: str) -> bytes:
        _decryptor = self.cipher.decryptor()
        _cipherbytes = b64decode(cipherb64)
        _padded_plainbytes = _decryptor.update(_cipherbytes) + _decryptor.finalize()
        _unpadder = self.padding.unpadder()
        return _unpadder.update(_padded_plainbytes) + _unpadder.finalize()


class Crypto_RSA(object):
    def __init__(self, size: int = 2048) -> None:
        self.private_key = generate_private_key(public_exponent=65537, key_size=size)  # public_exponent固定为65537或3
        self.public_key = self.private_key.public_key()
        self.public_pem = self.public_key.public_bytes(
            encoding=Encoding.PEM,
            format=PublicFormat.SubjectPublicKeyInfo
        ).decode()
        self.padding = OAEP(MGF1(algorithm=SHA256()), SHA256(), None)
        # OAEP（最优非对称加密填充算法）
        # MGF1（掩码生成函数1）的长度要不能小于填充算法的长度，不相同可能会有兼容性问题
        # OAEP下可加密的最大长度：keyLen(2048)/8 - 2*hashLen(256)/8 - 2 = 190字节

    def encrypt(self, plainbytes: bytes) -> str:
        _cipherbytes = self.public_key.encrypt(
            plainbytes,
            self.padding
        )
        return b64encode(_cipherbytes).decode()

    def decrypt(self, cipherb64: str) -> bytes:
        _cipherbytes = b64decode(cipherb64)
        return self.private_key.decrypt(
            _cipherbytes,
            self.padding
        )


default_rsa = Crypto_RSA()
