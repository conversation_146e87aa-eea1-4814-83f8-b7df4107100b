from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from typing import Coroutine, Any

from time import time
from asyncio import sleep
from os import urandom
from random import random
from uuid import UUID

from fastapi import (
    API<PERSON><PERSON>er,
    BackgroundT<PERSON><PERSON>,
    <PERSON><PERSON>,
    Depends,
    Header,
    Request
)
from fastapi.responses import JSONResponse

from .db import session_factory
from .crypto import default_rsa, Crypto_AES
from .log import Log_Fields, default_logger
from .batch import get_user_info, check_session_nonce, delete_redundant_session_nonce
from .misc import login_nonce_store, session_nonce_store
from .error import ErrorCode

router = APIRouter()


def init_log(
    request: Request,
    x_forwarded_for: str | None = Header(None)
) -> Log_Fields:
    if x_forwarded_for:
        ip = [x.strip() for x in x_forwarded_for.split(',')][-1]
    else:
        ip = request.client.host
    log = Log_Fields(
        ip=ip,
        method=request.method,
        path=request.url.path,
        start_time=time()
    )
    if request.url.query:
        log.query = request.url.query
    return log


def log_with_key(
    x_custom_encryption_key: str = Header(),
    log: Log_Fields = Depends(init_log)
) -> Log_Fields:
    try:
        log.key = default_rsa.decrypt(x_custom_encryption_key)
    except Exception as identifier:
        log.res_error = ErrorCode.ENCRYPTION_INVALID_KEY
        log.internal_error = 'default_rsa.decrypt(x_custom_encryption_key)'
        log.debug_error = str(identifier)
    return log


async def log_with_user_info_open(
    session_id: str = Cookie(),
    x_custom_session_token: str = Header(),
    log: Log_Fields = Depends(log_with_key)
) -> Log_Fields:
    if log.res_error != ErrorCode.OK:
        return log

    try:
        session_id = UUID(session_id)
    except Exception:
        log.res_error = ErrorCode.SESSION_MALFORMED_ID
        log.internal_error = 'UUID(session_id)'
        return log

    try:
        aes = Crypto_AES(log.key)
        session_token = aes.decrypt(x_custom_session_token)
    except Exception as identifier:
        log.res_error = ErrorCode.ENCRYPTION_INVALID_DATA
        log.internal_error = 'aes.decrypt(x_custom_session_token)'
        log.debug_error = str(identifier)
        return log

    session = session_factory()
    try:
        _session_and_user = await get_user_info(
            session,
            log.ip,
            session_id,
            session_token,
            log.start_time
        )
        await delete_redundant_session_nonce(session)
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.internal_error = 'get_user_info'
        log.debug_error = str(identifier)
        return log
    finally:
        await session.close()

    if 'user_id' in _session_and_user:
        log.user_id = _session_and_user['user_id']
    if 'error' in _session_and_user:
        log.res_error = _session_and_user['error']
        return log
    log.session_id = session_id
    log.session_tokens = _session_and_user['session_tokens']
    log.user_info = _session_and_user['user_info']

    if log.user_info['status'] & 4:  # user is being deleted
        log.res_error = ErrorCode.USER_NOT_FOUND
    return log


async def log_with_user_info(
    log: Log_Fields = Depends(log_with_user_info_open)
) -> Log_Fields:
    if log.res_error != ErrorCode.OK:
        return log

    nonce = int.from_bytes(log.key[128:])
    _result = await check_session_nonce(log.session_id, nonce)
    if _result == 0:
        log.res_error = ErrorCode.NONCE_INVALID
        return log
    if _result == 2:
        log.res_error = ErrorCode.NONCE_GET_NEW
        return log
    if log.user_info['status'] & 1:
        log.res_error = ErrorCode.PASSWORD_EXPIRED
        return log
    if log.user_info['status'] & 2:
        log.res_error = ErrorCode.PASSWORD_WEAK
    return log


async def background_run(log: Log_Fields, func: 'Coroutine', *args, **kwargs) -> None:
    # run in BackgroundTasks
    session = session_factory()
    try:
        await func(session, *args, **kwargs)
        await session.commit()
    except Exception as identifier:
        await default_logger.log('ERROR', log, server_error=func.__name__)
        await default_logger.log('DEBUG', log, server_error=str(identifier))
    await session.close()


def response_200(
    background_tasks: BackgroundTasks,
    log: Log_Fields,
    session_id: bool | None = False,
    session_token: bytes = b'',
    data: 'Any' = None
) -> JSONResponse:
    log.proc_time = time() - log.start_time
    background_tasks.add_task(default_logger.log, 'INFO', log)
    _body = {'isOK': True}
    if data:
        _body['data'] = data
    response = JSONResponse(_body)
    if session_id:
        response.set_cookie(
            'session_id',
            value=log.session_id.hex,
            expires='Fri, 31 Dec 9999 23:59:59 GMT',
            path='/api',
            samesite='strict'
        )
    elif session_id is None:
        response.set_cookie(
            'session_id',
            value='',
            expires='Thu, 01 Jan 1970 00:00:00 GMT',
            path='/api',
            samesite='strict'
        )
    if session_token:
        response.headers['x-custom-session-token'] = Crypto_AES(log.key).encrypt(session_token)
    return response


def response_err(
    background_tasks: BackgroundTasks,
    log: Log_Fields,
    error_extra: 'Any' = None
) -> JSONResponse:
    log.proc_time = time() - log.start_time
    if log.res_error.http_status == 500:
        background_tasks.add_task(default_logger.log, 'ERROR', log)
    else:
        background_tasks.add_task(default_logger.log, 'INFO', log)
    if log.debug_error != '-':
        background_tasks.add_task(default_logger.log, 'DEBUG', log)
    _body = {
        'isOK': False,
        'error': log.res_error.error_type,
        'errorCode': log.res_error.error_code,
        'errorMessage': log.res_error.error_message
    }
    if error_extra:
        _body['errorExtra'] = error_extra
    response = JSONResponse(
        _body,
        log.res_error.http_status
    )
    if log.res_error.http_status in (400, 403):
        response.headers['connection'] = 'close'
    return response


@router.get('/crypto/public')
def crypto_public(
    background_tasks: BackgroundTasks,
    log: Log_Fields = Depends(init_log)
) -> JSONResponse:
    return response_200(background_tasks, log, data=default_rsa.public_pem)


@router.get('/crypto/nonce1')
async def crypto_nonce1(
    background_tasks: BackgroundTasks,
    log: Log_Fields = Depends(init_log)
) -> JSONResponse:
    nonce = b'\x00' * 4 + urandom(4)
    await login_nonce_store.set(int.from_bytes(nonce), True)
    return response_200(background_tasks, log, data=list(nonce))


@router.get('/crypto/nonce2')
async def crypto_nonce2(
    background_tasks: BackgroundTasks,
    log: Log_Fields = Depends(log_with_user_info_open)
) -> JSONResponse:
    if log.res_error != ErrorCode.OK:
        response = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return response
    nonce = b'\x00' * 4 + urandom(4)
    session_nonce_store[log.session_id] = [int.from_bytes(nonce) - 1]
    return response_200(background_tasks, log, data=list(nonce))
