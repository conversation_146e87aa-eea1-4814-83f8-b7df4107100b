from logging import get<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>atter, _nameToLevel
from os import getenv
from uuid import UUID
from urllib.parse import quote_plus

debug = bool(getenv('DEBUG'))
INFLUXDB_URL = getenv('INFLUXDB_URL')
INFLUXDB_ORG = getenv('INFLUXDB_ORG')
INFLUXDB_TOKEN = getenv('INFLUXDB_TOKEN')
INFLUXDB_BUCKET = getenv('INFLUXDB_BUCKET')

from influxdb_client import InfluxDBClient
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync

from pydantic import BaseModel
from .error import ErrorCode


class Log_Fields(BaseModel):
    ip: str
    method: str
    path: str
    key: bytes = b''
    session_id: UUID = None
    session_tokens: list[str] = []
    user_id: UUID | str = '-'
    user_info: dict = {}
    start_time: float = 0.0
    proc_time: float = 0.0
    query: str = '-'
    req_params: str = '-'
    res_error: ErrorCode = ErrorCode.OK
    internal_error: str = '-'
    debug_error: str = '-'


class Logger(object):
    def __init__(self) -> None:
        self.logger = getLogger(__name__)
        self.logger.setLevel('DEBUG' if debug else 'INFO')
        _handler = StreamHandler()
        _handler.setFormatter(Formatter('%(asctime)s %(levelname)s %(message)s', r'%Y-%m-%dT%H:%M:%S%z'))
        self.logger.addHandler(_handler)
        self.influxdb_client = None
        self.influxdb_enable = False
        if INFLUXDB_URL and INFLUXDB_ORG and INFLUXDB_TOKEN and INFLUXDB_BUCKET:
            _client = InfluxDBClient(
                INFLUXDB_URL,
                INFLUXDB_TOKEN,
                debug=debug,
                timeout=1000,
                org=INFLUXDB_ORG
            )
            if _client.ping():
                _bucket = _client.buckets_api().find_bucket_by_name(INFLUXDB_BUCKET)
                if _bucket:
                    self.influxdb_enable = True
                else:
                    self.logger.warning(f'System: Unabled to connect influxdb bucket: {INFLUXDB_BUCKET}, ignored.')
            else:
                self.logger.warning(f'System: Unabled to connect influxdb: {INFLUXDB_URL}, ignored.')
            _client.close()
        else:
            self.logger.info('System: Influxdb not configured, ignored.')

    async def set_influxdb_client(self) -> None:
        # InfluxDBClientAsync不能在同步函数中创建
        if self.influxdb_enable:
            self.influxdb_client = InfluxDBClientAsync(
                INFLUXDB_URL,
                INFLUXDB_TOKEN,
                debug=debug,
                timeout=1000,
                org=INFLUXDB_ORG,
                default_tags={'app': 'data-midware'}
            )

    async def log(self, level: str, log: Log_Fields, server_error: str = None) -> None:
        level = level.upper()
        _error = log.debug_error if level == 'DEBUG' else log.internal_error
        if server_error:
            _error = server_error

        if isinstance(log.user_id, UUID):
            log.user_id = log.user_id.hex
        if level in _nameToLevel:
            self.logger.log(
                _nameToLevel[level],
                '%s "%s %s" %s %s %s %s "%s" "%s" %s "%s"',
                log.ip,
                log.method,
                log.path,
                log.user_id,
                log.res_error.http_status,
                log.start_time,
                log.proc_time,
                log.query,
                log.req_params,
                log.res_error.name,
                quote_plus(_error)
            )

        if not self.influxdb_enable:
            return
        if not self.influxdb_client:
            await self.set_influxdb_client()
        _writter = self.influxdb_client.write_api()
        try:
            await _writter.write(INFLUXDB_BUCKET, record={
                'measurement': 'data-midware',
                'tags': {'level': level, 'method': log.method, 'path': log.path},
                'fields': {
                    'ip': log.ip,
                    'user_id': log.user_id,
                    'status': log.res_error.http_status,
                    'start_time': log.start_time,
                    'proc_time': log.proc_time,
                    'query': log.query,
                    'req_params': log.req_params,
                    'res_error': log.res_error.name,
                    'server_error': _error
                }
            })
        except Exception as identifier:
            self.logger.error(f'System: Write influxdb failed: {identifier.status}')
            self.logger.debug(f'System: Write influxdb failed: {identifier.message}')


default_logger = Logger()
