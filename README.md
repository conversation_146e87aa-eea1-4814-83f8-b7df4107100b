# 设计

目录
- [1 功能](#1-功能)
  - [1.1 数据安全](#11-数据安全)
    - [1.1.1 防重放](#111-防重放)
    - [1.1.2 加密](#112-加密)
  - [1.2 访问控制](#12-访问控制)
  - [1.3 用户信息](#13-用户信息)
  - [1.4 应用数据](#14-应用数据)
  - [1.5 会话](#15-会话)
  - [1.6 用户配置](#16-用户配置)
  - [1.7 系统配置](#17-系统配置)
- [2 外部库](#2-外部库)
- [3 内部库](#3-内部库)
  - [3.1 数据模型](#31-数据模型)
  - [3.2 通用](#32-通用)
    - [3.2.1 类](#321-类)
      - [******* 加解密](#3211-加解密)
      - [******* 缓存空结果](#3212-缓存空结果)
      - [******* nonce缓存](#3213-nonce缓存)
      - [******* 错误码](#3214-错误码)
    - [3.2.2 数据操作](#322-数据操作)
      - [******* 读](#3221-读)
      - [******* 写](#3222-写)
    - [3.2.3 批处理](#323-批处理)
  - [3.3 本体](#33-本体)
    - [3.3.1 类](#331-类)
      - [3.3.1.1 缓存](#3311-缓存)
    - [3.3.2 数据操作](#332-数据操作)
    - [3.3.3 批处理](#333-批处理)
    - [3.3.4 参数模型](#334-参数模型)
  - [3.4 应用](#34-应用)
    - [3.4.1 类](#341-类)
    - [3.4.2 数据操作](#342-数据操作)
    - [3.4.3 批处理](#343-批处理)
    - [3.4.4 参数模型](#344-参数模型)
- [4 API](#4-api)
  - [4.1 共通](#41-共通)
  - [4.2 /api/crypto](#42-apicrypto)
  - [4.3 /api/user](#43-apiuser)
  - [4.4 /api/email](#44-apiemail)
  - [4.5 /api/system](#45-apisystem)
  - [4.6 /api/admin](#46-apiadmin)
    - [4.6.1 /users](#461-users)
    - [4.6.2 /user](#462-user)
  - [4.6 /api/app](#46-apiapp)
    - [4.6.1 /mfas](#461-mfas)
    - [4.6.2 /mfa](#462-mfa)
- [5 数据库](#5-数据库)
- [6 表结构](#6-表结构)
  - [6.1 本体](#61-本体)
  - [6.2 应用](#62-应用)
    - [6.2.1 MFA](#621-mfa)
- [7 日志](#7-日志)
  - [7.1 类](#71-类)
  - [7.2 参数模型](#72-参数模型)

## 1 功能

- 中间件指的是运行在应用和数据库之间，以API的形式提供数据操作。
  - 虽然暂无计划使用独立数据库。
- 数据包含[用户信息](#13-用户信息)，[应用数据](#14-应用数据)，[系统配置](#17-系统配置)。
- 数据操作包括数据增删改查和数据转换。
- 考虑成本等问题暂不考虑任何邮件外发功能。

### 1.1 数据安全

- 考虑到客户端和服务器所在网络可能不安全，所有敏感数据的传输需要加密，并且防重放攻击。
  - 客户端（公司网络）使用的代理可能使用专用的证书并解密https。
  - 服务端网络不使用https，存在被监听的风险。
- 敏感数据包括：
  - session_token
  - email
  - password
  - email2
  - 应用数据

#### 1.1.1 防重放

- 没有nonce或nonce失效或丢失时：
  - 需要先从服务器获取nonce值
    - 随机数
    - 和会话绑定
    - 保存在内存
  - 登录请求必须使用相同nonce
  - 其他请求每次需要nonce + 1
    - 只要不一样即可
      - 但要防止过去的nonce出现，所以要增长
    - 当达到最大值时需要重新获取nonce，而不是归零
      - 降低到达最大值的可能性
- 需要考虑并发请求的情况。
  - 以及任何问题导致接收nonce的顺序被打乱的情况。
- 以下功能无需nonce：
  - 注册
  - 重置密码

#### 1.1.2 加密

- 使用公钥加密*通信密钥*。
  - *通信密钥* = 随机数 + nonce
  - 使用*通信密钥*加密传输敏感数据。
- 使用用户邮箱加密数据密钥。
  - 使用数据密钥加密应用数据。
- 各随机数长度（字节）：
  - 通信密钥：128 + 8(nonce，最大值18446744073709551615)
  - 密码盐：32
  - 会话令牌：64
  - 数据密钥：256

### 1.2 访问控制

- 分为用户设置和系统设置，都有白名单和黑名单。
- 所有访问（API）的IP必须在用户**以及**系统的**任何**白名单内，并且不在**所有**黑名单内。
- 所有涉及用户的访问必须**同时**满足用户设置和系统设置，其他访问只须满足系统设置。
- IP信息从`x-forwarded-for`头或者socket获取。

### 1.3 用户信息

- 需要使用邮箱和密码登录，用户名仅用作昵称。
- 邮箱和密码都以哈希的形式保存，既能用于验证，又能保证安全。
- 在涉及敏感数据操作时可以进行**邮箱验证**。
  - 验证时需要用户使用该邮箱发送特定邮件，以证明用户拥有该邮箱。
  - 验证成功后一定时间内可以操作数据。
  - 此邮箱可以是另一个邮箱，保存哈希值，不公开，进一步增加账号以及**主**邮箱都被盗后的安全性。
    - 命名为安全邮箱。
    - 如果忘记（理论上）可以通过管理员重置。
- 建议用户使用巨复杂的不存在的邮箱作为主邮箱，并设置可以用的安全邮箱，并开启**邮箱验证**。

### 1.4 应用数据

- 应用数据必须加密。
- 因为用户可以忘记密码，所以不考虑使用用户密码加密任何数据。
- 但是用户不可以忘记登陆邮箱，并且服务器并不保存真正的邮箱地址，所以可以利用邮箱地址加密数据。
  - 在创建用户时生成随机的密钥，并使用邮箱地址进行加密。
  - 修改邮箱时仅需重新对密钥进行加密。
  - 使用密钥加解密数据。
  - 真正的密钥也就是邮箱地址由客户端保管，在需要的请求中附加加密后的邮箱地址。

### 1.5 会话

- 考虑移动端的IP会变化，所以会话不绑定IP，仅在会话中保存最后一次使用的IP。
  - 变化的IP依旧受访问控制限制。
  - 暂不考虑绑定国家。
- 会话令牌
  - 使用`x-custom-session-token`头传递。
  - 在数据库中只保存哈希值

### 1.6 用户配置

- 邮箱
- 密码
- 安全邮箱
- 用户名
- IP白名单
- IP黑名单
- 是否开启邮箱保护
- 锁定状态

### 1.7 系统配置

- 是否开放注册
- 密码复杂度
- 密码有效期
- 保留密码个数
- 历史记录保留个数
- 用户删除记录个数
- 会话超时时间
- 有效的旧会话令牌个数
- 每个用户最大会话个数
- **邮箱验证**有效期
- 全局IP白名单
- 全局IP黑名单
- 锁定前的登录失败次数
- 登录失败次数重置时间
- 账户锁定时间
- 是否仅锁定IP
- 惰性数据更新间隔

## 2 外部库

- uvicorn[standard]
  - Web服务器，负责接收和返回请求
  - 支持uvloop
- fastapi
  - API框架，负责处理请求
  - 现在不加`[standard]`相当于过去的fastapi-slim
  - 基于starlette和pydantic
- sqlalchemy
  - SQL数据库工具，ORM框架，简化数据库操作，免去SQL语句
  - 支持异步，需要额外的异步数据库驱动
- aiosqlite
  - 异步SQLite数据库驱动
- uuid7
  - 生成UUIDv7
- aiocache
  - 异步函数缓存，减少数据库访问
  - 支持Redis，Memcached，Memory
- cryptography
  - 用于数据哈希，加解密
- python-dotenv
  - 读取环境变量
- influxdb-client[async]
  - 用于上传日志到InfluxDB
  - 官方库
  - 支持异步

## 3 内部库

### 3.1 数据模型

见[表结构](#6-表结构)。

### 3.2 通用

#### 3.2.1 类

##### ******* 加解密

- Crypto_AES: object
  - 输入
    - password: bytes
  - 方法
    - encrypt(plainbytes: bytes) -> str
    - decrypt(cipherb64: str) -> bytes

- Crypto_RSA: object
  - 输入
    - size: int
  - 方法
    - encrypt(plainbytes: bytes) -> str
    - decrypt(cipherb64: str) -> bytes

##### ******* 缓存空结果

- CachedEmptyResult: Enum
  - SUCCESS: 0
  - FAILURE: 1
  - SKIP: 2

##### ******* nonce缓存

- login_nonce_store: aiocache.SimpleMemoryCache
  - key: int
  - value: bool
    - True
  - ttl: int
    - os.getenv('LOGIN_NONCE_STORE_TTL', 10)

- session_nonce_store: dict
  - key: UUID
    - session_id
  - value: list[int]

##### ******* 错误码

- ErrorCode: Enum

#### 3.2.2 数据操作

封装SQL操作。

不使用解包参数。

##### ******* 读

- get_all_users
  - 输入
    - email_hash: list[str] = None
    - user_id: list[UUID] = None
    - admin: bool = None
    - status: list[int] = None
    - name: str = None
    - protect: bool = None
  - 处理
    - _cols = [*tb_users_main.\_\_table\_\_.columns] + [*tb_users_other.\_\_table\_\_.columns]
    - _cols.remove(tb_users_main.id)
    - _cols.remove(tb_users_other.id)
    - _cols.remove(tb_users_other.user_id)
    - results = select(*_cols).join(tb_users_other)
    - if email_hash:
      - .where(tb_users_main.email_hash.in_(email_hash))
    - if user_id:
      - .where(tb_users_main.user_id.in_(user_id))
    - if admin is not None:
      - .where(tb_users_main.admin == admin)
    - if status:
      - .where(tb_users_main.status.in_(status))
    - if name:
      - .where(tb_users_main.name.like(f'%{name}%'))
    - if protect is not None:
      - .where(tb_users_main.protect == protect)
  - 输出: list[dict]
    - [dict(zip((col.name for col in _cols), row)) for row in results.all()]

- get_all_user_ids_in_session
  - 处理
    - results = select(tb_sessions.user_id).distinct()
  - 输出: ScalarResult
    - results.scalars()

- simple_select_one
  - 输入
    - table: Table
    - filter: dict
  - 处理
    - result = select(table)
    - for k, v in filter.items():
      - col = getattr(table, k)
      - .where(col == v)
  - 输出：Row
    - result.scalar()

- select_multi
  - 输入
    - table: Table
    - column: list[str]
    - in: dict[str, list[Any]] = None
    - gt: dict[str, Any] = None
    - lt: dict[str, Any] = None
    - order: dict[str, bool] = None
    - offset: int = None
    - limit: int = None
  - 处理
    - cols = []
    - if len(column) == 0:
      - column = table.\_\_table__.columns.keys()
    - for c in column:
      - cols.append(getattr(table, c))
    - results = select(*cols)
    - if in:
      - for k, v in in.items():
        - col = getattr(table, k)
        - .where(col.in_(v))
    - if gt:
      - for k, v in gt.items():
        - col = getattr(table, k)
        - .where(col > v)
    - if lt:
      - for k, v in lt.items():
        - col = getattr(table, k)
        - .where(col < v)
    - if order:
      - for k, desc in order.items():
        - col = getattr(table, k)
        - if desc:
          - .order_by(col.desc())
        - else:
          - .order_by(col)
    - if offset:
      - .offset(offset)
    - if limit:
      - .limit(limit)
  - 输出：list[dict]
    - [dict(zip(column, row)) for row in results.all()]

- get_count
  - 输入
    - table: Table
    - in: dict[str, list[Any]]
  - 处理
    - result = select(func.count()).select_from(table)
    - for k, v in in.items():
      - col = getattr(table, k)
      - .where(col.in_(v))
  - 输出：int
    - result.scalar()

##### ******* 写

- simple_insert_one
  - 输入
    - table: Table
    - value: dict
    - returning: List[str] = None
  - 处理
    - result = insert(table).values(value)
    - if returning:
      - col = []
      - for r in returning:
        - col.append(getattr(table, r))
      - .returning(*col)
  - 输出：tuple | None
    - result.first()

- simple_update_one
  - 输入
    - table: Table
    - filter: dict
    - value: dict
    - returning: List[str] = None
  - 处理
    - update(table).values(value)
    - for k, v in filter.items():
      - col = getattr(table, k)
      - .where(col == v)
    - if returning:
      - col = []
      - for r in returning:
        - col.append(getattr(table, r))
      - .returning(*col)
  - 输出：tuple | None
    - result.first()

- simple_upsert_one
  - 输入
    - table: Table
    - value: dict
    - index: list[str]
  - 处理
    - insert(table).values(value)
      - .on_conflict_do_update(index_elements=index, set_=value)

- delete_multi
  - 输入
    - table: Table
    - in: dict[str, list[Any]] = None
    - gt: dict[str, Any] = None
    - lt: dict[str, Any] = None
  - 处理
    - delete(table)
    - if in:
      - for k, v in in.items():
        - col = getattr(table, k)
        - .where(col.in_(v))
    - if gt:
      - for k, v in gt.items():
        - col = getattr(table, k)
        - .where(col > v)
    - if lt:
      - for k, v in lt.items():
        - col = getattr(table, k)
        - .where(col < v)

#### 3.2.3 批处理

不直接调用SQL操作。

可以使用解包参数，并转换为明确参数向下传递。

- update_user_session
  - 缓存：os.getenv('SESSION_TOKEN_UPDATE_INTERVAL', 600)
  - 输入
    - session_id: str
    - session_tokens: list[str]
      - 不缓存
    - ip: str
      - 不缓存
    - refresh_time: float = time.time()
      - 不缓存
  - 处理
    - user_session_token_fallback = get_system_config().user_session_token_fallback
    - new_session_token = os.urandom(64)
    - session_tokens.insert(0, get_hash(new_session_token, algorithm='md5'))
    - while len(session_tokens) > max(user_session_token_fallback + 1, 1):
      - session_tokens.pop()
    - simple_update_one(tb_users_session, {'session_id': session_id}, {'session_token': session_tokens, 'ip': ip, 'refresh_time': refresh_time})
  - 输出：tuple[bytes, float]
    - new_session_token
    - refresh_time

- get_user_info
  - 输入
    - ip: str
    - session_id: UUID
    - session_token: bytes
    - time: float = time.time()
  - 处理
    - system_config = get_system_config()
    - system_allow_ip = system_config.system_allow_ip
    - system_deny_ip = system_config.system_deny_ip
    - user_session_expire = system_config.user_session_expire
    - delete_expired_users_session(time)
    - delete_redundant_users_session()
    - session = simple_select_one(tb_users_session, {'session_id': session_id})
    - user_session_number = get_system_config().user_session_number
    - results = select_multi(tb_users_session, ['session_id'], {'user_id': [session.user_id]}, order={'id': True}, limit=user_session_number)
    - if session_id not in [row['session_id'] for row in results]: return {}
    - if get_hash(session_token, algorithm='md5') not in session.session_token: return {}
    - if time - session.refresh_time > user_session_expire: return {}
    - user = get_all_users(user_id=[session.user_id])
    - if ip != session.ip:
      - 为了节省性能
      - 用户在误屏蔽自己的IP之后在logout之前依旧有机会挽救
      - match_ip(ip, [allow_ip, system_allow_ip], [deny_ip, system_deny_ip])
  - 输出: dict
    - user_id: UUID
    - session_id: UUID
    - session_tokens: list[str]
      - session.session_token
    - user_info: dict
      - user[0]
    - error: str
      - 'session'
      - 'ip'

- delete_expired_users_session
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 输入
    - time: float
      - 不缓存
  - 处理
    - user_session_expire = get_system_config().user_session_expire
    - delete_multi(tb_users_session, lt={'refresh_time': time - user_session_expire})
  - 输出：CachedEmptyResult

- delete_redundant_users_session
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 处理
    - user_session_number = get_system_config().user_session_number
    - sids = []
    - uids = await get_all_user_ids_in_session()
    - for user_id in uids:
      - r = select_multi(tb_users_session, ['session_id'], {'user_id': [user_id]}, order={'id': True}, offset=user_session_number)
      - sids.extend(r)
    - if len(sids) == 0: return CachedEmptyResult(2)
    - delete_multi(tb_users_session, {'session_id': [row['session_id'] for row in sids]})
  - 输出：CachedEmptyResult

- check_session_nonce：在验证session之后
  - 输入
    - session_id: UUID
    - nonce: int
  - 处理
    - if session_id not in session_nonce_store:
      - return 2
    - if nonce in session_nonce_store[session_id]:
      - return 0
    - if nonce < min(session_nonce_store[session_id]):
      - return 0
    - if nonce >= 18446744073709551615:
      - session_nonce_store.pop(session_id)
    - else:
      - session_nonce_store[session_id].append(nonce)
    - _task = session_nonce_store.get('delete_task', None)
    - if _task:
      - _task.cancel()
    - session_nonce_store['delete_task'] = asyncio.create_task(delete_old_nonce(session_id, nonce))
  - 输出：int
    - 0: 无效
    - 1: 有效
    - 2: 需要重新获取

- delete_old_nonce
  - 输入
    - session_id: UUID
    - nonce: int
  - 处理
    - await asyncio.sleep(os.getenv('SESSION_NONCE_STORE_CLEAR_TIMEOUT', 10))
    - session_nonce_store[session_id] = [x for x in session_nonce_store[session_id] if x >= nonce]

- delete_redundant_session_nonce
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 处理
    - results = await select_multi(tb_users_session, ['session_id'])
    - exists = [row['session_id'] for row in results]
    - for session_id in list(session_nonce_store.keys()):
      - if session_id == 'delete_task': continue
      - if session_id not in exists:
        - session_nonce_store.pop(session_id)
  - 输出：CachedEmptyResult

### 3.3 本体

#### 3.3.1 类

##### 3.3.1.1 缓存

保存临时数据，目前只用于**邮箱验证**。

- email_verify_store: aiocache.SimpleMemoryCache
  - key: str
    - from
  - value: str
    - to
  - ttl: int
    - system_email_verify_expire

#### 3.3.2 数据操作

无

#### 3.3.3 批处理

不直接调用SQL操作。

可以使用解包参数，并转换为明确参数向下传递。

- get_system_config
  - 缓存：永久
  - 输出: Row
    - simple_select_one(tb_system_config, {'id': 0})

- add_user
  - 输入
    - email: bytes
    - password: bytes
    - admin: bool = False
    - email2: bytes = b''
    - name: str = f'user_{generate_random_string()}
    - allow_ip: list[str] = ['0.0.0.0/0', '::/0']
    - deny_ip: list[str] = []
    - protect: bool = False
    - time: float = time.time()
    - ip: str = 'system'
  - 处理
    - password_salt = os.urandom(32)
    - user_id, = simple_insert_one(tb_users_main, {'email': get_hash(email), 'email_mask': email.decode()[:3], 'password': get_hash(password, password_salt), 'password_salt': password_salt, 'data_key': Crypto_AES(email).encrypt(os.urandom(256)), 'admin': admin, 'status': 0}, returning=['user_id'])
    - simple_insert_one(tb_users_other, {'user_id': user_id, 'email2': get_hash(email2), 'email2_mask': email2.decode()[:3], 'name': name, 'allow_ip': allow_ip, 'deny_ip': deny_ip, 'protect': protect})
    - insert_history_config(user_id, time, ip, email_mask=email.decode()[:3], password=0, email2_mask=email2.decode()[:3], name=name, allow_ip=allow_ip, deny_ip=deny_ip, protect=protect)
  - 输出: UUID
    - user_id

- check_cidr
  - 输入
    - cidr: str
  - 处理
    - try ipaddress.ip_network(cidr, strict=False)
  - 输出: ipaddress.IPv4Network | ipaddress.IPv6Network | None

- match_ip
  - 输入
    - ip: str
    - allow_ip: [[str]]
    - deny_ip: [[str]]
  - 处理
    - try _ip = ipaddress.ip_address(ip)
    - for _allow_ip in allow_ip:
      - found = False
      - for _cidr in _allow_ip:
        - if _ip in check_cidr(_cidr):
          - found = True
          - break
      - if not found: return False
    - for _deny_ip in deny_ip:
      - for _cidr in _deny_ip:
        - if _ip in check_cidr(_cidr):
          - return False
  - 输出: bool

- get_hash
  - 输入
    - data: bytes
    - salt: bytes = b''
    - algorithm: str = 'shake_256'
  - 处理
    - digest = hashlib.new(algorithm)
    - digest.update(data)
    - if salt: digest.update(salt)
    - if algorithm == 'shake_256':
      - result = digest.digest(64)
    - elif algorithm == 'shake_128':
      - result = digest.digest(32)
    - else:
      - result = digest.digest()
  - 输出：str
    - base64.b64encode(result).decode()

- check_password_compliance
  - 输入
    - password: str
  - 处理
    - get_system_config()
      - 获取
        - user_passwd_require_digit
        - user_passwd_require_upper
        - user_passwd_require_lower
        - user_passwd_require_special
        - user_passwd_require_unicode
        - user_passwd_require_length
  - 输出: bool

- generate_random_string
  - 输入
    - length: int = 4
    - digit: bool = True
    - upper: bool = True
    - lower: bool = True
    - special: bool = False
  - 处理
    - dic = ''
    - if digit: dic += string.digits
    - if upper: dic += string.ascii_uppercase
    - if lower: dic += string.ascii_lowercase
    - if special: dic += string.punctuation
  - 输出: str
    - ''.join(random.choice(dic) for i in range(length))

- insert_history_login
  - 输入
    - user_id: UUID
    - time: float
    - ip: str
    - result: int
  - 处理
    - user_history_login_number = get_system_config().user_history_login_number
    - if user_history_login_number != 0:
      - simple_insert_one(tb_users_history_login, {'user_id': user_id, 'time': time, 'ip': ip, 'result': result})
    - delete_old_users_history_login(user_id)

- delete_old_users_history_login
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 输入
    - user_id: UUID
  - 处理
    - user_history_login_number = get_system_config().user_history_login_number
    - if user_history_login_number <= 0: return CachedEmptyResult(2)
    - results = select_multi(tb_users_history_login, ['id'], {'user_id': [user_id]}, order={'id': True}, offset=user_history_login_number)
    - delete_multi(tb_users_history_login, {'id': [row['id'] for row in results]})
  - 输出：CachedEmptyResult

- insert_history_passwd
  - 输入
    - user_id: UUID
    - password: str
    - time: float
  - 处理
    - user_history_passwd_number = get_system_config().user_history_passwd_number
    - if user_history_passwd_number != 0:
      - simple_insert_one(tb_users_history_passwd, {'user_id': user_id, 'password': password, 'time': time})
    - delete_old_users_history_passwd(user_id)

- delete_old_users_history_passwd
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 输入
    - user_id: UUID
  - 处理
    - user_history_passwd_number = get_system_config().user_history_passwd_number
    - if user_history_passwd_number <= 0: return CachedEmptyResult(2)
    - results = select_multi(tb_users_history_passwd, ['id'], {'user_id': [user_id]}, order={'id': True}, offset=user_history_passwd_number)
    - delete_multi(tb_users_history_passwd, {'id': [row['id'] for row in results]})
  - 输出：CachedEmptyResult

- insert_history_config
  - 输入
    - user_id: UUID
    - time: float
    - ip: str
    - **values: HistoryConfig
  - 处理
    - user_history_config_number = get_system_config().user_history_config_number
    - if user_history_config_number != 0:
      - simple_insert_one(tb_users_history_config, {'user_id': user_id, 'time': time, 'ip': ip, **values})
    - delete_old_users_history_config(user_id)

- delete_old_users_history_config
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 输入
    - user_id: UUID
  - 处理
    - user_history_config_number = get_system_config().user_history_config_number
    - if user_history_config_number <= 0: return CachedEmptyResult(2)
    - results = select_multi(tb_users_history_config, ['id'], {'user_id': [user_id]}, order={'id': True}, offset=user_history_config_number)
    - delete_multi(tb_users_history_config, {'id': [row['id'] for row in results[:-1]]})
  - 输出：CachedEmptyResult

- insert_history_delete
  - 输入
    - email: str
    - time: float
    - ip: str
  - 处理
    - user_history_delete_number = get_system_config().user_history_delete_number
    - if user_history_delete_number != 0:
      - simple_insert_one(tb_users_history_delete, {'email': email, 'time': time, 'ip': ip})
    - delete_old_users_history_delete()

- delete_old_users_history_delete
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 处理
    - user_history_delete_number = get_system_config().user_history_delete_number
    - if user_history_delete_number <= 0: return CachedEmptyResult(2)
    - results = select_multi(tb_users_history_delete, ['id'], order={'id': True}, offset=user_history_delete_number)
    - delete_multi(tb_users_history_delete, {'id': [row['id'] for row in results]})
  - 输出：CachedEmptyResult

- delete_expired_users_lock
  - 缓存：os.getenv('DATA_CLEAR_INTERVAL', 3600)
  - 输入
    - time: float
      - 不缓存
  - 处理
    - user_lock_period = get_system_config().user_lock_period
    - login_fail_count_reset_period = get_system_config().login_fail_count_reset_period
    - if user_lock_period >= 0:
      - delete_multi(tb_users_lock, lt={'time': time - user_lock_period})
    - delete_multi(tb_users_login_fail, lt={'time': time - login_fail_count_reset_period})
  - 输出：CachedEmptyResult

#### 3.3.4 参数模型

- HistoryConfig: pydantic.BaseModel
  - email_mask: str = None
  - password: int = None
  - email2_mask: str = None
  - name: str = None
  - allow_ip: list[str] = None
  - deny_ip: list[str] = None
  - protect: bool = None
  - locked: bool = None

- SystemConfig: pydantic.BaseModel
  - system_allow_register: bool = None
  - user_passwd_require_digit: bool = None
  - user_passwd_require_upper: bool = None
  - user_passwd_require_lower: bool = None
  - user_passwd_require_special: bool = None
  - user_passwd_require_unicode: bool = None
  - user_passwd_require_length: int = None
  - user_passwd_expire: int = None
  - user_history_login_number: int = None
  - user_history_passwd_number: int = None
  - user_history_config_number: int = None
  - user_history_delete_number: int = None
  - user_session_expire: int = None
  - user_session_token_fallback: int = None
  - user_session_number: int = None
  - user_lock_period: int = None
  - user_lock_ip_only: bool = None
  - login_fail_lock_number: int = None
  - login_fail_count_reset_period: int = None
  - system_email_verify_expire: int = None
  - system_allow_ip: list[str] = None
  - system_deny_ip: list[str] = None

### 3.4 应用

#### 3.4.1 类

- Totp: object
  - 输入
    - secret: bytes
    - algorithm: str = 'SHA1'
    - interval: int = 30
    - digits: int = 6
    - results: int = 10
  - 方法
    - codes(time: float = time.time()) -> list[str]

#### 3.4.2 数据操作

无

#### 3.4.3 批处理

无

#### 3.4.4 参数模型

- MFAConfig: pydantic.BaseModel
  - protect: bool

- MFANewData: pydantic.BaseModel
  - name: str
  - comment: str = None
  - secret: str
  - order: int
  - algorithm: Literal['SHA1', 'SHA256', 'SHA512'] = 'SHA1'
  - interval: int = 30
  - digits: Literal[6, 7, 8] = 6

- MFAUpdateData: pydantic.BaseModel
  - name: str = None
  - comment: str = None
  - secret: str = None
  - order: int = None
  - algorithm: Literal['SHA1', 'SHA256', 'SHA512'] = None
  - interval: int = None
  - digits: Literal[6, 7, 8] = None

## 4 API

### 4.1 共通

- 使用RESTful格式，更直观
  - POST: 新建，重置
  - PUT: 更新
  - DELETE: 删除
  - GET: 通过query传参
    - get：对单数端点
    - list：对复数端点
- `x-custom-encryption-key`头 = 使用公钥加密后的*通信密钥*
  - 响应中包含敏感信息也是用*通信密钥*加密
- 会话信息只在返回200时更新（set-cookie）
- set-cookie的值使用hex编码，不包含特殊字符
- 响应体
  - isOK: bool
  - data?: any
  - error?: str
  - errorCode?: int
  - errorMessage?: str
- 正常的返回码
  - 200:
    - isOK: true
- 错误代码
  - 位数
    - 服务器错误：1
    - 客户端错误
      - 可恢复：2
      - 数据错误：3
      - 限制：4
      - 畸形请求：5
      - 其他：6
  - 开头
    - 加密相关：1
    - nonce：2
    - 会话：3
    - 用户信息：4
    - 应用数据：5
    - 访问控制：6
    - 邮件验证：7
    - 系统：8
    - 其他：9
- 异常的返回码
  - 303:
    - isOK: false
    - error: password
      - message: expired
        - code: 41
      - message: weak
        - code: 42
    - error: nonce
      - message: get a new nonce
        - code: 20
  - 400
    - isOK: false
    - error: nonce
      - message: invalid
        - code: 20000
    - error: encryption
      - message: invalid data encryption
        - code: 101
    - error: session
      - message: malformed session id
        - code: 30001
  - 401
    - isOK: false
    - error: session
      - message: not found
        - code: 301
      - message: replaced by new login
        - code: 302
      - message: session token wrong
        - code: 303
      - message: expired
        - code: 304
  - 403
    - isOK: false
    - error: ip
      - message: forbidden
        - code: 6001
  - 404
    - isOK: false
    - error: user
      - message: not found
        - code: 401
  - 406
    - isOK: false
    - error: encryption
      - message: invalid encryption key
        - code: 102
  - 500
    - isOK: false
    - error: server
      - message: internal error
        - code: 0
- 返回以下错误时关闭连接（connection: close）
  - 400
  - 403
- 前置处理
  - 获取
    - now = time.time()
    - ip = Header()['x-forwarded-for']\[-1]
    - session_id = Cookie()['session_id']
    - session_token = Header()['x-custom-session-token']
    - key = Header()['x-custom-encryption-key']
  - 使用*私钥*解密*通信密钥*：Crypto_RSA.decrypt(key)
  - 使用*通信密钥*解密session_token：Crypto_AES(key).decrypt(session_token)
  - get_user_info(ip, session_id, session_token, now) or return
  - delete_redundant_session_nonce()
  - if url.endswith('/crypto/nonce2'): return
  - nonce = key[128:]
  - nonce = int.from_bytes(nonce)
  - check_session_nonce(session_id, nonce) or return

### 4.2 /api/crypto

- /public: 用于加密POST请求中的邮箱和密码，服务器启动时随机生成*私钥*，GET
  - 输出
    - isOK: bool
    - data: str
      - pem
- /nonce1: 用于获取登录nonce，GET
  - 处理
    - data = b'\x00' * 4 + os.urandom(4)
    - await login_nonce_store.set(int.from_bytes(nonce), True, ttl=os.getenv('LOGIN_NONCE_STORE_TTL', 10))
  - 输出
    - isOK: bool
    - data: list[int]
      - list(data)
- /nonce2: 用于更新会话nonce，GET
  - 处理
    - 前置处理
    - data = b'\x00' * 4 + os.urandom(4)
    - session_nonce_store[session_id] = [int.from_bytes(nonce) - 1]
  - 输出
    - isOK: bool
    - data: list[int]
      - list(data)

### 4.3 /api/user

- POST：注册
  - 输入
    - email: str
    - password: str
    - email2?: str = ''
    - name?: str = f'user_{generate_random_string()}'
    - allowIp?: [str] = ['0.0.0.0/0', '::/0']
    - denyIp?: [str] = []
    - protect?: bool = False
  - 处理
    - 获取
      - now = time.time()
      - ip = Header()['x-forwarded-for']\[-1]
      - key: Header()['x-custom-encryption-key']
    - system_config = get_system_config()
    - if not system_config.system_allow_register: return
    - system_allow_ip = system_config.system_allow_ip
    - system_deny_ip = system_config.system_deny_ip
    - match_ip(ip, [system_allow_ip], [system_deny_ip])
    - True
      - 使用*私钥*解密*通信密钥*：Crypto_RSA.decrypt(key)
      - 使用*通信密钥*解密email, password, email2：Crypto_AES(key).decrypt(...)
      - if email_verify_store.get(get_hash(email)) != 'register': return
      - simple_select_one(tb_users_main, {'email': get_hash(email)})
        - await email_verify_store.delete(get_hash(email))
        - 存在：return
      - check_password_compliance(password) or return
      - for _cidr in allowIp:
        - if not check_cidr(_cidr): return
      - for _cidr in denyIp: check_cidr(_cidr)
        - if not check_cidr(_cidr): return
      - await email_verify_store.delete(get_hash(email))
      - add_user(email, password, False, email2, name, allowIp, denyIp, protect, now, ip)
  - 输出
    - isOK: bool
    - error: str
      - 403
        - system: not open(8001)
      - 404
        - verify email: not found(700)
      - 406
        - cidr invalid: *cidr*(90001)
      - 409
        - email: already exists(403)
- DELETE: 需要会话cookie
  - 处理
    - 前置处理
      - 获取user_id, session_tokens
      - 获取email, email2, protect, admin
    - if protect:
      - _email = email if not email2 else email2
      - if email_verify_store.get(_email) != user_id.hex: return
      - email_verify_store.delete(_email)
    - if admin:
      - if len(get_all_users(admin=True)) <= 1:
        - return
    - simple_update_one(tb_users_main, {'user_id': user_id}, {'status': 4})
    - BackgroundTasks.add_task(delete_multi, tb_users_main, {'user_id': [user_id]})
    - insert_history_delete(email, now, ip)
  - 输出
    - isOK: bool
    - error: str
      - 404
        - verify email: not found(700)
      - 406
        - system: last admin user(8002)
  - 头
    - set-cookie: session_id=''; path=/api; expires=Thu, 01 Jan 1970 00:00:00 GMT; sameSite=Strict
- /session
  - POST: 登录
    - 输入
      - email: str
      - password: str
    - 处理
      - 获取
        - now = time.time()
        - ip = Header()['x-forwarded-for']\[-1]
        - key: Header()['x-custom-encryption-key']
      - 使用*私钥*解密*通信密钥*：Crypto_RSA.decrypt(key)
      - 使用*通信密钥*解密email, password：Crypto_AES(key).decrypt(...)
      - nonce = key[128:]
      - nonce = int.from_bytes(nonce)
      - if not login_nonce_store.get(nonce): return
      - get_all_users(email_hash=[get_hash(email)])
        - 获取user_id, password, password_salt, status
        - 获取allow_ip, deny_ip
      - 不存在：return
      - simple_select_one(tb_users_other, {'user_id': user_id})
      - get_system_config()
        - 获取
          - user_passwd_expire
          - login_fail_lock_number
          - login_fail_count_reset_period
          - user_lock_period
          - user_lock_ip_only
          - system_deny_ip
          - system_allow_ip
      - delete_expired_users_lock(now)
      - match_ip(ip, [allow_ip, system_allow_ip], [deny_ip, system_deny_ip])
      - False
        - insert_history_login(user_id, now, ip, 4)
      - if user_lock_ip_only:
        - select_multi(tb_users_lock, ['id'], {'user_id': [user_id], 'ip': [ip]}, gt={'time': now - user_lock_period})
      - else:
        - select_multi(tb_users_lock, ['id'], {'user_id': [user_id]}, gt={'time': now - user_lock_period})
      - 存在
        - insert_history_login(user_id, now, ip, 3)
        - return
      - （不存在）
      - 哈希password：get_hash(password, password_salt)
      - 验证password, status
      - 成功
        - await login_nonce_store.delete(nonce)
        - select_multi(tb_users_history_passwd, ['time'], {'user_id': [user_id]}, order={'id': True}, limit=1)
          - 获取time
        - 验证user_passwd_expire
        - 失败
          - new_status = 1
        - check_password_compliance(password)
        - 失败
          - new_status = 2 | new_status
        - simple_update_one(tb_users_main, {'user_id': user_id}, {'status': new_status})
        - insert_history_login(user_id, now, ip, 0)
        - delete_multi(tb_users_login_fail, {'user_id': [user_id], 'ip': [ip]})
        - session_token = os.urandom(64)
        - session_id, = simple_insert_one(tb_users_session, {'session_token': [get_hash(session_token, algorithm='md5')], 'user_id': user_id, 'ip': ip, 'login_time': now, 'refresh_time': now}, ['session_id'])
        - await login_nonce_store.delete(nonce)
        - session_nonce_store[session_id] = [nonce]
      - 失败
        - select_multi(tb_users_login_fail, ['count'], {'user_id': [user_id], 'ip': [ip]}, gt={'time': now - login_fail_count_reset_period})
          - 获取count
        - 存在
          - count++
        - 不存在
          - count = 1
        - if count > login_fail_lock_number:
          - simple_upsert_one(tb_users_lock, {'user_id': user_id, 'ip': ip, 'time': now}, ['user_id', 'ip'])
          - delete_multi(tb_users_login_fail, {'user_id': [user_id], 'ip': [ip]})
          - insert_history_login(user_id, now, ip, 2)
          - insert_history_config(user_id, now, ip, locked=True)
        - else
          - simple_upsert_one(tb_users_login_fail, {'user_id': user_id, 'ip': ip, 'count': count, 'time': now}, ['user_id', 'ip'])
          - insert_history_login(user_id, now, ip, 1)
    - 输出
      - isOK: bool
      - error
        - 401
          - password: wrong(404)
        - 403
          - locked: *time*(6002)
        - 404
          - email: not found(402)
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(session_token)
      - set-cookie: session_id=session_id; path=/api; expires=Fri, 31 Dec 9999 23:59:59 GMT; sameSite=Strict
  - DELETE: 登出，需要会话cookie
    - 输入
      - target_session_id?: str
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
      - if target_session_id:
        - simple_select_one(tb_users_session, {'session_id': target_session_id, 'user_id': user_id})
        - 不存在: return
        - delete_multi(tb_users_session, {'session_id': [target_session_id]})
        - new_session_token, time_ = update_user_session(session_id, session_tokens, ip, now)
        - if log.start_time != time_:
          - new_session_token = b''
      - else:
        - delete_multi(tb_users_session, {'session_id': [session_id]})
        - new_session_token = b''
    - 输出
      - isOK: bool
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- /sessions
  - GET: 获取当前用户会话，需要会话cookie
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
      - user_session_number = get_system_config().user_session_number
      - data = select_multi(tb_users_session, ['session_id', 'ip', 'login_time'], {'user_id': [user_id]}, order={'id': True}, limit=user_session_number)
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - data: list[dict]
        - session_id: str
        - ip: str
        - login_time: number
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- /config: 需要会话cookie
  - GET
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
        - 获取email_mask, email2_mask, name, allow_ip, deny_ip, protect
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - data: Object
        - user_id: str
        - email_mask: str
        - email2_mask: str
        - name: str
        - allow_ip: list[str]
        - deny_ip: list[str]
        - protect: bool
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - PUT: 修改其他属性（不需要验证当前设定）
    - 输入
      - name?: str
      - allowIp?: [str]
      - denyIp?: [str]
      - newProtect?: bool
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
        - 获取email, email2, protect
      - data = {}
      - if name:
        - data['name'] = name
      - if allowIp:
        - for _cidr in allowIp:
          - if not check_cidr(_cidr): return
        - data['allow_ip'] = allowIp
      - if denyIp:
        - for _cidr in denyIp:
          - if not check_cidr(_cidr): return
        - data['deny_ip'] = denyIp
      - if newProtect:
        - data['protect'] = protect
      - if protect:
        - _email = email if not email2 else email2
        - if email_verify_store.get(_email) != user_id.hex: return
        - email_verify_store.delete(_email)
      - insert_history_config(user_id, now, ip, **data)
      - simple_update_one(tb_users_other, {'user_id': user_id}, data)
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - error: str
        - 404
          - verify email: not found(700)
        - 406
          - cidr invalid: *cidr*(90001)
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - /email: 修改邮箱，PUT
    - 输入
      - curEmail: str
      - newEmail: str
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
        - 获取email, email2, protect
      - 使用*通信密钥*解密email, newEmail：Crypto_AES(key).decrypt(...)
      - if get_hash(curEmail) != email: return
      - if protect:
        - _email = email if not email2 else email2
        - if email_verify_store.get(_email) != user_id.hex: return
        - email_verify_store.delete(_email)
      - insert_history_config(user_id, now, ip, email_mask=newEmail[:3])
      - 使用curEmail解密data_key
      - 使用newEmail加密data_key
      - simple_update_one(tb_users_main, {'user_id': user_id}, {'email': get_hash(newEmail), 'email_mask': newEmail[:3], 'data_key': 加密data_key})
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - error: str
        - 401
          - email: wrong(405)
        - 404
          - verify email: not found(700)
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - /email2: 修改邮箱2，PUT
    - 输入
      - curEmail2: str
      - newEmail2: str
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
        - 获取email, email2, protect
      - 使用*通信密钥*解密curEmail2, newEmail2: Crypto_AES(key).decrypt(...)
      - if protect:
        - _email = email if not email2 else email2
        - if email_verify_store.get(_email) != user_id.hex: return
        - email_verify_store.delete(_email)
      - if email2:
        - if get_hash(curEmail2) != email2: return
      - else:
        - if curEmail2: return
      - insert_history_config(user_id, now, ip, email2_mask=newEmail2[:3])
      - simple_update_one(tb_users_other, {'user_id': user_id}, {'email2': get_hash(newEmail2), 'email2_mask': newEmail2[:3]})
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - error: str
        - 401
          - email2: wrong(406)
        - 404
          - verify email: not found(700)
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - /password
    - PUT: 修改密码
      - 输入
        - curPassword: str
        - newPassword: str
      - 处理
        - 前置处理
          - 获取user_id, session_tokens
          - 获取password, password_salt
          - 获取email, email2, protect
        - 使用*通信密钥*解密curPassword, newPassword: Crypto_AES(key).decrypt(...)
        - check_password_compliance(newPassword) or return
        - if get_hash(curPassword, password_salt) != password: return
        - if curPassword == newPassword: return
        - simple_select_one(tb_users_history_passwd, {'user_id': user_id, 'password': get_hash(newPassword, password_salt)})
        - 存在: return
        - if protect:
          - _email = email if not email2 else email2
          - if email_verify_store.get(_email) != user_id.hex: return
          - email_verify_store.delete(_email)
        - insert_history_passwd(user_id, password, now)
        - insert_history_config(user_id, now, ip, password=1)
        - simple_update_one(tb_users_main, {'user_id': user_id}, {'password': get_hash(newPassword, password_salt), 'status': 0})
        - new_session_token = update_user_session(session_id, session_tokens, ip, now)
      - 输出
        - isOK: bool
        - error: str
          - 401
            - password: wrong(404)
          - 404
            - verify email: not found(700)
          - 406
            - new password: weak(407)|used(408)
      - 头
        - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
    - POST: 重置密码，邮箱验证依旧需要email2，如果有
      - 输入
        - email: str
        - newPassword: str
      - 处理
        - 获取
          - now = time.time()
          - ip = Header()['x-forwarded-for']\[-1]
          - key: Header()['x-custom-encryption-key']
        - 使用*私钥*解密*通信密钥*: Crypto_RSA.decrypt(key)
        - 使用*通信密钥*解密email, newPassword: Crypto_AES(key).decrypt(...)
        - get_all_users(email=[get_hash(email)])
          - 获取user_id, password, password_salt, email2
        - 不存在：retrun
        - _email = email if not email2 else email2
        - if email_verify_store.get(_email) != 'password': return
        - check_password_compliance(newPassword) or return
        - if get_hash(newPassword) == password: return
        - simple_select_one(tb_users_history_passwd, {'user_id': user_id, 'password': get_hash(newPassword, password_salt)})
        - 存在: return
        - email_verify_store.delete(_email)
        - insert_history_passwd(user_id, password, now)
        - insert_history_config(user_id, now, ip, password=2)
        - simple_update_one(tb_users_main, {'user_id': user_id}, {'password': get_hash(newPassword, password_salt), 'status': 0})
      - 输出
        - isOK: bool
        - error: str
          - 404
            - email: not found(402)
            - verify email: not found(700)
          - 406
            - new password: weak(407)|used(408)
- /locks: 需要会话cookie
  - GET
    - 输入
      - start?: int = 1
      - end?: int = 10
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
      - data = select_multi(tb_users_lock, ['ip', 'time'], {'user_id': [user_id]}, order={'id': True}, offset=start + 1, limit=max(end - start + 1, 0))
      - count = get_count(tb_users_lock, {'user_id': [user_id]})
      - new_session_token = update_user_session(session_id, session_tokens, ip)
    - 输出
      - isOK: bool
      - data: list[dict]
        - ip: str
        - time: number
      - total: int
        - count
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - DELETE: 解除锁定IP
    - 输入
      - ips: list[str]
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
      - delete_multi(tb_users_lock, {'user_id': [user_id], 'ip': ips})
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- /history: 需要会话cookie, GET
  - /logins
    - 输入
      - start?: int = 1
      - end?: int = 10
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
      - data = select_multi(tb_users_history_login, ['time', 'ip','result'], {'user_id': [user_id]}, order={'id': True}, offset=start + 1, limit=max(end - start + 1, 0))
      - count = get_count(tb_users_history_login, {'user_id': [user_id]})
      - new_session_token = update_user_session(session_id, session_tokens, ip)
    - 输出
      - isOK: bool
      - data: list[dict]
        - time: number
        - ip: str
        - result: number
      - total: int
        - count
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - /configs
    - 输入
      - start?: int = 1
      - end?: int = 10
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
      - results = select_multi(tb_users_history_config, [], {'user_id': [user_id]}, order={'id': True}, offset=start + 1, limit=max(end - start + 1, 0))
      - data = []
      - for row in results:
        - row.pop('id')
        - row.pop('user_id')
        - for key, value in row.items():
          - if value is None:
            - row.pop(key)
        - data.append(row)
      - count = get_count(tb_users_history_config, {'user_id': [user_id]})
      - new_session_token = update_user_session(session_id, session_tokens, ip)
    - 输出
      - isOK: bool
      - data: list[dict]
        - time: number
        - ip: str
        - <非空字段>: str|number
        - <非空字段>: ...
      - total: int
        - count
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)

### 4.4 /api/email

- POST: 从外部邮件服务发送
  - 输入
    - from: str
    - to: str
    - signature: str
  - 处理
    - try RSAPublicKey().verify(signature.encode(), f'{from}.{to}'.encode())
    - system_email_verify_expire = get_system_config().system_email_verify_expire
    - email_verify_store.set(get_hash(from.encode()), to, system_email_verify_expire * 60)
  - 输出
    - isOK: bool
    - error: str
      - 403
        - signature: wrong(199)

### 4.5 /api/system

- /config: 开放
  - GET
    - 处理
      - 获取
        - ip = Header()['x-forwarded-for']\[-1]
      - match_ip(ip, [system_allow_ip], [system_deny_ip])
      - True
        - system_config = get_system_config()
        - data = dict(system_config)
        - data.pop('id')
    - 输出
      - isOK: bool
      - data: dict
  - PUT：需要管理员会话cookie
    - 输入
      - config: SystemConfig
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
        - 验证admin
      - simple_update_one(tb_system_config, {'id': 0}, {**config})
      - get_system_config.cache.clear()
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - error
        - 403
          - system: not admin(8003)
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- /ping: **WebSocket**
  - 处理
    - 获取
      - ws = WebSocket()
    - timer = asyncio.Event()
    - count = 0
    - await ws.accept()
    - while count < 5:
      - data = await asyncio.wait_for(ws.recive_bytes(), timeout=10) or break
      - await ws.send_bytes(data)
      - count += 1

### 4.6 /api/admin

需要管理员会话cookie。

#### 4.6.1 /users

- GET
  - 处理
    - 前置处理
      - 获取user_id, session_tokens
      - 验证admin
    - data = get_all_users()
    - new_session_token = update_user_session(session_id, session_tokens, ip)
  - 输出
    - isOK: bool
    - error
      - 403
        - system: not admin(8003)
    - data: list[dict]
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)

#### 4.6.2 /user

- POST
  - 输入
    - email: str
    - password: str
    - admin?: bool = False
    - email2?: str = ''
    - name?: str = f'user_{generate_random_string()}'
    - allowIp?: [str] = ['0.0.0.0/0', '::/0']
    - denyIp?: [str] = []
    - protect?: bool = False
  - 处理
    - 前置处理
      - 获取user_id, session_tokens
      - 验证admin
    - 使用*通信密钥*解密email, password, email2：Crypto_AES(key).decrypt(...)
    - simple_select_one(tb_users_main, {'email': get_hash(email)})
      - 存在：return
    - for _cidr in allowIp:
      - if not check_cidr(_cidr): return
    - for _cidr in denyIp: check_cidr(_cidr)
      - if not check_cidr(_cidr): return
    - add_user(email, password, False, email2, name, allowIp, denyIp, protect, now, 'admin')
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 406
        - cidr invalid: *cidr*(90001)
      - 409
        - email: already exists(403)
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- PUT
  - 输入
    - userId: str
    - newPassword?: str = None
    - status?: number = None
    - newEmail2?: str = None
    - name?: str = None
    - allowIp?: [str] = None
    - denyIp?: [str] = None
    - protect?: bool = None
  - 处理
    - 前置处理
      - 获取user_id, session_tokens
      - 验证admin
    - simple_select_one(tb_users_main, {'user_id': userId})
      - 获取password, password_salt, data_key, admin
    - 不存在：return
    - if admin: return
    - main_data = {}
    - other_data = {}
    - history_data = {}
    - if newPassword:
      - 使用*通信密钥*解密newPassword: Crypto_AES(key).decrypt(newPassword)
      - main_data['password'] = get_hash(newPassword, password_salt)
      - history_data['password'] = 2
      - insert_history_passwd(userId, password, now)
    - if status:
      - main_data['status'] = status
    - if newEmail2:
      - 使用*通信密钥*解密newEmail2: Crypto_AES(key).decrypt(newEmail2)
      - other_data['email2'] = get_hash(newEmail2)
      - other_data['email2_mask'] = newEmail2[:3]
      - history_data['email2_mask'] = newEmail2[:3]
    - if name:
      - other_data['name'] = name
      - history_data['name'] = name
    - if allowIp:
      - for _cidr in allowIp:
        - if not check_cidr(_cidr): return
      - other_data['allow_ip'] = allowIp
      - history_data['allow_ip'] = allowIp
    - if denyIp:
      - for _cidr in denyIp: check_cidr(_cidr)
        - if not check_cidr(_cidr): return
      - other_data['deny_ip'] = denyIp
      - history_data['deny_ip'] = denyIp
    - if protect:
      - other_data['protect'] = protect
      - history_data['protect'] = protect
    - simple_update_one(tb_users_main, {'user_id': userId}, {**main_data})
    - simple_update_one(tb_users_other, {'user_id': userId}, {**other_data})
    - insert_history_config(userId, now, 'admin', **history_data)
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 404
        - user: not found(401)
      - 406
        - cidr invalid: *cidr*(90001)
        - system: target user is admin(8004)
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- DELETE
  - 输入
    - userId: str
  - 处理
    - 前置处理
      - 获取user_id, session_tokens
      - 验证admin
    - simple_select_one(tb_users_main, {'user_id': userId})
      - 获取email, admin, status
    - 不存在：return
    - if admin: return
    - simple_update_one(tb_users_main, {'user_id': userId}, {'status': status|4})
    - insert_history_delete(email, now, 'admin')
    - BackgroundTasks.add_task(delete_multi, tb_users_main, {'user_id': [userId]})
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 404
        - user: not found(401)
      - 406
        - system: target user is admin(8004)
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)

### 4.6 /api/app

管理员不能使用。

#### 4.6.1 /mfas

- GET
  - 输入
    - curEmail: Header()['x-custom-email']
    - start?: int = 1
    - limit?: int = 10
  - 处理
    - 前置处理
      - 获取user_id, session_tokens, email, data_key
      - 验证admin
    - 使用*通信密钥*解密curEmail: Crypto_AES(key).decrypt(curEmail)
    - if get_hash(curEmail) != email: return
    - data_key = Crypto_AES(curEmail).decrypt(data_key)
    - data = select_multi(tb_apps_mfa_main, [], {'user_id': [user_id]}, order={'order': False}, offset=start + 1, limit=max(end - start + 1, 0))
    - count = get_count(tb_apps_mfa_main, {'user_id': [user_id]})
    - for row in data:
      - 使用data_key解密secret: Crypto_AES(data_key).decrypt(row['secret'])
      - _codes = Totp(解密secret, row['algorithm'], row['digits'], row['interval']).codes(now)
      - _codes = json.dumps(_codes)
      - _codes = Crypto_AES(key).encrypt(_codes.encode())
      - row['codes'] = _codes
      - row['id'] = row['id'].hex
      - row.pop('secret')
      - row.pop('user_id')
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 406
        - email: invalid(501)
    - data: list[str]
    - time: now
    - total: count
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)

#### 4.6.2 /mfa

- POST
  - 输入
    - curEmail: Header()['x-custom-email']
    - data: MFANewData
  - 处理
    - 前置处理
      - 获取user_id, session_tokens, email, data_key
      - 验证admin
    - 使用*通信密钥*解密curEmail: Crypto_AES(key).decrypt(curEmail)
    - if get_hash(curEmail) != email: return
    - data_key = Crypto_AES(curEmail).decrypt(data_key)
    - 使用*通信密钥*解密secret: Crypto_AES(key).decrypt(data['secret'])
    - try Totp(解密secret, data['algorithm'], data['digits'], data['interval']).codes(now)
    - 使用data_key加密secret: Crypto_AES(data_key).encrypt(解密secret)
    - data['secret'] = 加密secret
    - data['created_time'] = now
    - data['updated_time'] = now
    - simple_insert_one(tb_apps_mfa_main, {'user_id': user_id, **data})
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 403
        - app: not for admin(8005)
      - 406
        - email: invalid(501)
        - secret: invalid(50001)
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- PUT
  - 输入
    - curEmail: Header()['x-custom-email']
    - id: str
    - data: MFAUpdateData
  - 处理
    - 前置处理
      - 获取user_id, session_tokens, email, email2, data_key
      - 验证admin
    - simple_select_one(tb_apps_mfa_main, {'user_id': user_id, 'id': uuid.UUID(id)})
    - 不存在: return
    - if not data: return
    - 使用*通信密钥*解密curEmail: Crypto_AES(key).decrypt(curEmail)
    - protect = simple_select_one(tb_apps_mfa_config, {'user_id': user_id}).protect
    - if protect:
      - _email = email if not email2 else email2
      - if email_verify_store.get(_email) != user_id.hex: return
      - email_verify_store.delete(_email)
    - if get_hash(curEmail) != email: return
    - data_key = Crypto_AES(curEmail).decrypt(data_key)
    - if 'secret' in data:
      - 使用*通信密钥*解密secret: Crypto_AES(key).decrypt(data['secret'])
      - 使用data_key加密secret: Crypto_AES(data_key).encrypt(解密secret)
      - data['secret'] = 加密secret
      - data['updated_time'] = now
    - simple_update_one(tb_apps_mfa_main, {'user_id': user_id, 'id': uuid.UUID(id)}, {**data})
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 403
        - app: not for admin(8005)
      - 404
        - verify email: not found(700)
        - id: not found(502)
      - 406
        - email: invalid(501)
        - secret: invalid(50001)
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- GET：导出密钥
  - 输入
    - curEmail: str
    - ids: list[str]
  - 处理
    - 前置处理
      - 获取user_id, session_tokens, email, email2, data_key
      - 验证admin
    - 使用*通信密钥*解密curEmail: Crypto_AES(key).decrypt(curEmail)
    - protect = simple_select_one(tb_apps_mfa_config, {'user_id': user_id}).protect
    - if protect:
      - _email = email if not email2 else email2
      - if email_verify_store.get(_email) != user_id.hex: return
      - email_verify_store.delete(_email)
    - if get_hash(curEmail) != email: return
    - data_key = Crypto_AES(curEmail).decrypt(data_key)
    - results = select_multi(tb_apps_mfa_main, ['id', 'secret'], {'user_id': [user_id], 'id': [uuid.UUID(id) for id in ids]})
    - data = []
    - for row in results:
      - 使用data_key解密secret: Crypto_AES(data_key).decrypt(row['secret'])
      - 使用key加密secret: Crypto_AES(key).encrypt(解密secret)
      - data.append({'id': row['id'].hex, 'secret': 加密secret})
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
    - error: str
      - 406
        - email: invalid(501)
    - data: list[dict]
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- DELETE
  - 输入
    - ids?: list[str] = None
  - 处理
    - 前置处理
      - 获取user_id, session_tokens, email, email2
      - 验证admin
    - protect = simple_select_one(tb_apps_mfa_config, {'user_id': user_id}).protect
    - if protect:
      - _email = email if not email2 else email2
      - if email_verify_store.get(_email) != user_id.hex: return
      - email_verify_store.delete(_email)
    - if ids:
      - delete_multi(tb_apps_mfa_main, {'user_id': [user_id], 'id': [uuid.UUID(id) for id in ids]})
    - else:
      - delete_multi(tb_apps_mfa_main, {'user_id': [user_id]})
    - new_session_token = update_user_session(session_id, session_tokens, ip, now)
  - 输出
    - isOK: bool
  - 头
    - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
- /config
  - GET
    - 处理
      - 前置处理
        - 获取user_id, session_tokens
        - 验证admin
      - mfa_config = simple_select_one(tb_apps_mfa_config, {'user_id': user_id})
      - data = dict(mfa_config)
      - data.pop('id')
      - data.pop('user_id')
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - data: dict
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)
  - PUT
    - 输入
      - config: MFAConfig
    - 处理
      - 前置处理
        - 获取user_id, session_tokens, email, email2
        - 验证admin
      - protect = simple_select_one(tb_apps_mfa_config, {'user_id': user_id}).protect
      - if protect:
        - _email = email if not email2 else email2
        - if email_verify_store.get(_email) != user_id.hex: return
        - email_verify_store.delete(_email)
      - simple_update_one(tb_apps_mfa_config, {'user_id': user_id}, {**config})
      - new_session_token = update_user_session(session_id, session_tokens, ip, now)
    - 输出
      - isOK: bool
      - error: str
        - 403
          - app: not for admin(8005)
        - 404
          - verify email: not found(700)
    - 头
      - x-custom-session-token: Crypto_AES(key).encrypt(new_session_token)

## 5 数据库

选用SQLite。

- 要件
  - 嵌入式：免去单独设置服务器
  - 文档型：支持复杂数据结构
  - 原生支持Python
  - 高性能：不使用纯Python编写
- 参数
  - synchronous: OFF
  - foreign_keys: ON
  - temp_store: MEMORY
  - cache_size: -1048576
    - 1GiB
    - 正数表示page数，负数表示KiB

## 6 表结构

### 6.1 本体

- tb_users_main
  - id: string, primary key
    - UUIDv7, 基于时间和随机数的递增ID
    - v8算法定义和v7类似，但是允许厂家自定义算法所以不选用
  - user_id: string, unique
    - UUIDv4
  - email: string, unique
    - SHAKE256，输出512位以上时安全级别与SHA3-512相同（256位），速度略快，输出长度不限
    - 不可能重复，如果重复，即视为邮箱地址重复
  - email_mask: string
    - *email*的前三个字符
  - password: string
    - SHAKE256
  - password_salt: binary
    - 不更新
  - data_key: string
    - 使用*email*进行AES-GCM加密
    - 用于应用数据加解密
  - admin: bool
    - 管理员**只**可以管理系统及用户设置
    - 不可注册
  - status: number
    - `0`: 正常
    - `1`: 密码过期
    - `2`: 密码不符合要求
    - `4`: 正在删除
      - 考虑删除账号可能花时间，禁止登录正在删除的账号

- tb_users_other
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - email2: string
    - SHAKE256
    - 仅用于**邮箱验证**操作，如果空则使用*email*
    - 空时就是空，不是空的哈希值
    - 修改时必须进行**邮箱验证**
  - email2_mask: string
  - name: string
    - 仅用于显示
    - 默认：`user<随机数>`
  - allow_ip: [string]
    - 默认：`['0.0.0.0/0', '::/0']`
  - deny_ip: [string]
    - 默认：`[]`
  - protect: bool
    - 修改任何用户设置需要**邮箱验证**
    - 默认：`false`

- tb_users_login_fail
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - ip: string
  - count: number
  - time: number
    - *当前时间*

- tb_users_lock
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - ip: string
  - time: number
    - *当前时间*

- tb_users_session
  - id: string, primary key
    - UUIDv7
  - session_id: string, unique
    - UUIDv4
  - session_token: [string]
    - MD5
    - 个数有限，MD5足够
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - ip: string
  - login_time: number
    - *当前时间*
    - 不再更新
  - refresh_time: number
    - *当前时间*

- tb_users_history_login
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - time: number
    - *当前时间*
  - ip: string
  - result: number
    - `0`: 成功
    - `1`: 密码错误
    - `2`: 密码错误+锁定
    - `3`: 已锁定
    - `4`: IP阻止

- tb_users_history_passwd: 包含当前密码
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - password: string
  - time: number

- tb_users_history_config
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - time: number
    - *当前时间*
  - ip: string
  - email_mask: string, null
  - password: number, null
    - `0`: 创建用户
    - `1`: 修改密码
    - `2`: 重置密码
  - email2_mask: string, null
  - name: string, null
  - allow_ip: [string], null
  - deny_ip: [string], null
  - protect: bool, null
  - locked: bool, null

- tb_users_history_delete
  - id: string, primary key
    - UUIDv7
  - email: string, index
  - time: number
  - ip: string

- tb_system_config
  - id: number, primary key, auto increment
    - 固定：`0`
  - system_allow_register: bool
    - 默认：`false`
  - user_passwd_require_digit: bool
    - 默认：`false`
  - user_passwd_require_upper: bool
    - 默认：`false`
  - user_passwd_require_lower: bool
    - 默认：`false`
  - user_passwd_require_special: bool
    - 默认：`false`
  - user_passwd_require_unicode: bool
    - 默认：`false`
  - user_passwd_require_length: number
    - 默认：`0`, 任意
  - user_passwd_expire: number
    - 天数
    - 默认：`-1`, 永久
  - user_history_login_number: number
    - 默认：`10000`
    - `-1`: 无限
  - user_history_passwd_number: number
    - 默认：`0`
    - `-1`: 无限
  - user_history_config_number: number
    - 默认：`2000`
    - `-1`: 无限
  - user_history_delete_number: number
    - 默认：`1000`
    - `-1`: 无限
  - user_session_expire: number
    - 分钟
    - 默认：`10080`, 7天
  - user_session_token_fallback: number
    - 默认：`1`
  - user_session_number: number
    - 默认：`3`
  - user_lock_period: number
    - 分钟
    - 默认：`60`
    - `-1`: 永久
  - user_lock_ip_only: bool
    - 默认：`true`
  - login_fail_lock_number: number
    - 输错的次数，包括第一次
    - 默认：`5`
  - login_fail_count_reset_period: number
    - 分钟
    - 默认：`30`
  - system_email_verify_expire: number
    - 分钟
    - 默认：`10`
  - system_allow_ip: [string]
    - 默认：`['0.0.0.0/0', '::/0']`
  - system_deny_ip: [string]
    - 默认：`[]`

### 6.2 应用

创建用户时不会自动创建，不存在时视为默认值。

#### 6.2.1 MFA

- tb_apps_mfa_main
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - name: string
  - comment: string, null
  - secret: string
    - 使用data_key加密
  - order: number
    - 允许相同
  - algorithm: string
    - 默认：`SHA1`
    - 可选：`SHA256`, `SHA512`
  - interval: number
    - 秒
    - 默认：`30`
  - digites: number
    - 默认：`6`
    - 可选：`7`, `8`
  - created_time: number
  - updated_time: number
    - 仅密钥更新的时间

- tb_apps_mfa_config
  - id: string, primary key
    - UUIDv7
  - user_id: string, index, foreign key tb_users_main(user_id) on delete cascade
  - protect: bool
    - 更新，删除时需要**邮箱验证**
    - 默认：`false`

## 7 日志

使用Logging模块。

- 格式：使用空格分隔
  - time：ISO8601格式，精确到秒，带时区
  - level：DEBUG, INFO, WARNING, ERROR
  - ip
  - "method path"
  - user_id
  - status
  - start_time
  - proc_time
  - query
  - req_params
  - ErrorCode.name
  - error

可选用InfluxDB作为外部日志服务。

- 要件
  - 时序列数据库
  - 支持保存字符串
  - 官方支持Python
- 通用格式
  - measurement: data-midware
  - tags
    - level
    - method
    - path
  - time: unix时间戳
- 记录内容
  - INFO: API请求
    - fields
      - ip
      - user_id
      - status
      - start_time
      - proc_time
      - query
      - req_params: 请求参数
      - res_error: 响应错误码
  - 其他：错误、警告、调试等
    - fields
      - server_error: 错误信息

### 7.1 类

- Logger
  - 方法
    - async set_influxdb_client()
    - async log(level: str, log: Log_Fields, error: str = '-')

### 7.2 参数模型

- Log_Fields: BaseModel
  - ip: str
  - method: str
  - path: str
  - key: bytes = b''
  - session_id: UUID = None
  - session_tokens: list[str] = []
  - user_id: UUID | str = '-'
  - user_info: dict = {}
  - start_time: float = 0.0
  - proc_time: float = 0.0
  - query: str = '-'
  - req_params: str = '-'
  - res_error: ErrorCode = ErrorCode.OK
  - internal_error: str = '-'
  - debug_error: str = '-'
