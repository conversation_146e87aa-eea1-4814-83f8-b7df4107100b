from enum import Enum


class ErrorCode(Enum):
    OK = (-1, 200, '-', '-')  # Code, Status, Error, Message

    SERVER_INTERNAL_ERROR = (0, 500, 'Server', 'Internal error.')

    ENCRYPTION_INVALID_DATA = (101, 400, 'Encryption', 'Invalid data encryption.')
    ENCRYPTION_INVALID_KEY = (102, 406, 'Encryption', 'Invalid encryption key.')
    ENCRYPTION_INVALID_SIGNATURE = (199, 406, 'Signature', 'Invalid signature.')

    NONCE_GET_NEW = (20, 303, 'Nonce', 'Get a new nonce.')
    NONCE_INVALID = (20000, 400, 'Nonce', 'Invalid nonce.')

    SESSION_MALFORMED_ID = (30001, 400, 'Session', 'Malformed session id.')
    SESSION_NOT_FOUND = (301, 401, 'Session', 'Not found.')
    SESSION_REPLACED = (302, 401, 'Session', 'Replaced by new login.')
    SESSION_TOKEN_WRONG = (303, 401, 'Session', 'Session token wrong.')
    SESSION_EXPIRED = (304, 401, 'Session', 'Expired.')

    PASSWORD_EXPIRED = (41, 303, 'Password', 'Expired.')
    PASSWORD_WEAK = (42, 303, 'Password', 'Too weak.')
    USER_NOT_FOUND = (401, 404, 'User', 'Not found.')
    EMAIL_NOT_FOUND = (402, 404, 'Email', 'Not found.')
    EMAIL_ALREADY_EXISTS = (403, 409, 'Email', 'Already exists.')
    PASSWORD_WRONG = (404, 401, 'Password', 'Wrong.')
    EMAIL_WRONG = (405, 401, 'Email', 'Wrong.')
    EMAIL2_WRONG = (406, 401, 'Email2', 'Wrong.')
    NEW_PASSWORD_WEAK = (407, 406, 'Password', 'New password too weak.')
    NEW_PASSWORD_USED = (408, 406, 'Password', 'New password used before.')

    APP_EMAIL_WRONG = (501, 401, 'Email', 'Invalid email.')
    APP_ID_NOT_FOUND = (502, 404, 'ID', 'Not found.')
    APP_MFA_SECRET_INVALID = (50001, 406, 'Secret', 'Invalid secret.')

    IP_FORBIDDEN = (6001, 403, 'IP', 'Client IP is not allowed.')
    USER_LOCKED = (6002, 403, 'Lock', 'User locked.')

    VERIFY_EMAIL_NOT_FOUND = (700, 404, 'Verify', 'Verify email not found.')

    SYSTEM_REGISTER_FORBIDDEN = (8001, 403, 'System', 'Register is not allowed.')
    SYSTEM_LAST_ADMIN = (8002, 406, 'System', 'Last admin user.')
    SYSTEM_NEED_ADMIN = (8003, 403, 'System', 'Require admin.')
    SYSTEM_DELETE_ADMIN = (8004, 406, 'System', 'Target user is admin.')
    APP_NOT_FOR_ADMIN = (8005, 403, 'App', 'Not for admin.')

    OTHER_CIDR_INVALID = (90001, 406, 'CIDR', 'Invalid CIDR.')
    OTHER_PARAMETER_REQUIRED = (90002, 406, 'Parameter', 'Parameter required.')

    def __init__(self, error_code: int, http_status: int, error_type: str, error_message: str):
        self.error_code = error_code
        self.http_status = http_status
        self.error_type = error_type
        self.error_message = error_message
