from .._main.table import *  # noqa
from .._app.mfa.table import *  # noqa

from .db import engine, session_factory, Base
from .sql import simple_select_one, simple_insert_one
from .._main.batch import get_system_config, add_user

from .log import default_logger

from os import getenv
debug = bool(getenv('DEBUG'))


async def on_startup():
    conn = await engine.connect()
    await conn.run_sync(Base.metadata.create_all)
    await conn.commit()
    await conn.close()
    session = session_factory()
    system_config = await get_system_config(session)
    if not system_config:
        await simple_insert_one(
            session,
            TB_SYSTEM_CONFIG,  # noqa
            {
                'id': 0,
                'system_allow_register': True,
                'user_passwd_require_digit': False,
                'user_passwd_require_upper': False,
                'user_passwd_require_lower': False,
                'user_passwd_require_special': False,
                'user_passwd_require_unicode': False,
                'user_passwd_require_length': 2,
                'user_passwd_expire': -1,
                'user_history_login_number': 10000,
                'user_history_passwd_number': 1,
                'user_history_config_number': 2000,
                'user_history_delete_number': 1000,
                'user_session_expire': 2,
                'user_session_token_fallback': 1,
                'user_session_number': 3,
                'user_lock_period': 1,
                'user_lock_ip_only': True,
                'login_fail_lock_number': 1,
                'login_fail_count_reset_period': 1,
                'system_email_verify_expire': 10,
                'system_allow_ip': ['0.0.0.0/0', '::/0'],
                'system_deny_ip': []
            }
        )
        await session.commit()
    admin = await simple_select_one(
        session,
        TB_USERS_MAIN,  # noqa
        {'admin': True}
    )
    if not admin:
        await add_user(
            session,
            b'admin@local',
            b'admin',
            admin=True,
            name='admin'
        )
        await session.commit()
    await session.close()
    await default_logger.set_influxdb_client()
    default_logger.logger.info('System: Started.')


async def on_shutdown():
    if default_logger.influxdb_client:
        await default_logger.influxdb_client.close()
    default_logger.logger.info('System: Stopped.')


from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import PlainTextResponse
from starlette.exceptions import HTTPException

from .. import __version__

app = FastAPI(
    title="Data Middleware",
    description="A API server.",
    version=__version__,
    openapi_url='/api/openapi.json' if debug else None,
    redirect_slashes=False,
    docs_url='/api/docs' if debug else None,
    redoc_url=None,
    on_startup=[on_startup],
    on_shutdown=[on_shutdown]
)


@app.exception_handler(HTTPException)
def http_error(request: Request, exc: HTTPException) -> PlainTextResponse:
    response = PlainTextResponse(str(exc.detail), exc.status_code)
    if exc.status_code < 500:
        response.headers['connection'] = 'close'
    return response


@app.exception_handler(RequestValidationError)
def validation_error(request: Request, exc: RequestValidationError) -> PlainTextResponse:
    return PlainTextResponse('Bad Request', 400, {'connection': 'close'})


from .api import router as router_share
from .._main.api import router as router_main

app.include_router(router_share, prefix='/api', tags=['share'])
app.include_router(router_main, prefix='/api', tags=['main'])
