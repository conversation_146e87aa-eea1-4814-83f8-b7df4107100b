from sqlalchemy import Column, Uuid, Text, Integer, Boolean, Float, BINARY, JSON, ForeignKey, UniqueConstraint

from uuid import uuid4
from uuid_extensions import uuid7

from .._share.db import Base


class TB_USERS_MAIN(Base):
    __tablename__ = 'tb_users_main'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, default=uuid4, unique=True)
    email = Column(Text, unique=True, nullable=False)
    email_mask = Column(Text, nullable=False)
    password = Column(Text, nullable=False)
    password_salt = Column(BINARY, nullable=False)
    data_key = Column(Text, nullable=False)
    admin = Column(Boolean, nullable=False)
    status = Column(Integer, nullable=False)


class TB_USERS_OTHER(Base):
    __tablename__ = 'tb_users_other'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    email2 = Column(Text, nullable=False)
    email2_mask = Column(Text, nullable=False)
    name = Column(Text, nullable=False)
    allow_ip = Column(JSON, nullable=False)
    deny_ip = Column(JSON, nullable=False)
    protect = Column(Boolean, nullable=False)


class TB_USERS_LOGIN_FAIL(Base):
    __tablename__ = 'tb_users_login_fail'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    ip = Column(Text, nullable=False)
    count = Column(Integer, nullable=False)
    time = Column(Float, nullable=False)
    __table_args__ = (UniqueConstraint('user_id', 'ip', name='unique_userid_ip'),)


class TB_USERS_LOCK(Base):
    __tablename__ = 'tb_users_lock'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    ip = Column(Text, nullable=False)
    time = Column(Float, nullable=False)
    __table_args__ = (UniqueConstraint('user_id', 'ip', name='unique_userid_ip'),)


class TB_USERS_SESSION(Base):
    __tablename__ = 'tb_users_session'
    id = Column(Uuid, default=uuid7, primary_key=True)
    session_id = Column(Uuid, default=uuid4, unique=True)
    session_token = Column(JSON, nullable=False)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    ip = Column(Text, nullable=False)
    login_time = Column(Float, nullable=False)
    refresh_time = Column(Float, nullable=False)


class TB_USERS_HISTORY_LOGIN(Base):
    __tablename__ = 'tb_users_history_login'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    time = Column(Float, nullable=False)
    ip = Column(Text, nullable=False)
    result = Column(Integer, nullable=False)


class TB_USERS_HISTORY_PASSWD(Base):
    __tablename__ = 'tb_users_history_passwd'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    password = Column(Text, nullable=False)
    time = Column(Float, nullable=False)


class TB_USERS_HISTORY_CONFIG(Base):
    __tablename__ = 'tb_users_history_config'
    id = Column(Uuid, default=uuid7, primary_key=True)
    user_id = Column(Uuid, ForeignKey('tb_users_main.user_id', onupdate='CASCADE', ondelete='CASCADE'), index=True)
    time = Column(Float, nullable=False)
    ip = Column(Text, nullable=False)
    email_mask = Column(Text)
    password = Column(Integer)
    email2_mask = Column(Text)
    name = Column(Text)
    allow_ip = Column(JSON)
    deny_ip = Column(JSON)
    protect = Column(Boolean)
    locked = Column(Boolean)


class TB_USERS_HISTORY_DELETE(Base):
    __tablename__ = 'tb_users_history_delete'
    id = Column(Uuid, default=uuid7, primary_key=True)
    email = Column(Text, index=True, nullable=False)
    time = Column(Float, nullable=False)
    ip = Column(Text, nullable=False)


class TB_SYSTEM_CONFIG(Base):
    __tablename__ = 'tb_system_config'
    id = Column(Integer, autoincrement=True, primary_key=True)
    system_allow_register = Column(Boolean, nullable=False)
    user_passwd_require_digit = Column(Boolean, nullable=False)
    user_passwd_require_upper = Column(Boolean, nullable=False)
    user_passwd_require_lower = Column(Boolean, nullable=False)
    user_passwd_require_special = Column(Boolean, nullable=False)
    user_passwd_require_unicode = Column(Boolean, nullable=False)
    user_passwd_require_length = Column(Integer, nullable=False)
    user_passwd_expire = Column(Float, nullable=False)
    user_history_login_number = Column(Integer, nullable=False)
    user_history_passwd_number = Column(Integer, nullable=False)
    user_history_config_number = Column(Integer, nullable=False)
    user_history_delete_number = Column(Integer, nullable=False)
    user_session_expire = Column(Integer, nullable=False)
    user_session_token_fallback = Column(Integer, nullable=False)
    user_session_number = Column(Integer, nullable=False)
    user_lock_period = Column(Integer, nullable=False)
    user_lock_ip_only = Column(Boolean, nullable=False)
    login_fail_lock_number = Column(Integer, nullable=False)
    login_fail_count_reset_period = Column(Integer, nullable=False)
    system_email_verify_expire = Column(Integer, nullable=False)
    system_allow_ip = Column(JSON, nullable=False)
    system_deny_ip = Column(JSON, nullable=False)


__all__ = [
    'TB_USERS_MAIN',
    'TB_USERS_OTHER',
    'TB_USERS_LOGIN_FAIL',
    'TB_USERS_LOCK',
    'TB_USERS_SESSION',
    'TB_USERS_HISTORY_LOGIN',
    'TB_USERS_HISTORY_PASSWD',
    'TB_USERS_HISTORY_CONFIG',
    'TB_USERS_HISTORY_DELETE',
    'TB_SYSTEM_CONFIG',
]
