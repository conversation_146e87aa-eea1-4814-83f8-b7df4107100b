from time import time
from uuid import UUID
from urllib.parse import u<PERSON><PERSON><PERSON>
from os import urandom
from asyncio import sleep
from random import random

from fastapi import APIRouter, Body, BackgroundTasks, Depends
from fastapi.responses import JSONResponse

from .batch import (
    get_system_config,
    check_cidr,
    match_ip,
    get_hash,
    check_password_compliance,
    generate_random_string,
    add_user,
    insert_history_login,
    insert_history_config,
    insert_history_delete,
    delete_expired_users_lock
)
from .table import (
    TB_USERS_MAIN,
    TB_USERS_OTHER,
    TB_USERS_LOCK,
    TB_USERS_LOGIN_FAIL,
    TB_USERS_SESSION,
    TB_USERS_HISTORY_PASSWD
)
from .misc import email_verify_store

from .._share.db import session_factory
from .._share.crypto import Crypto_AES
from .._share.sql import (
    get_all_users,
    simple_select_one,
    select_multi,
    simple_insert_one,
    simple_update_one,
    simple_upsert_one,
    delete_multi
)
from .._share.batch import update_user_session
from .._share.log import Log_Fields, default_logger
from .._share.api import (
    log_with_key,
    log_with_user_info,
    background_run,
    response_200,
    response_err
)
from .._share.misc import login_nonce_store, session_nonce_store
from .._share.error import ErrorCode

router = APIRouter()


@router.post('/user')
# content-type: application/json required
async def user_create(
    background_tasks: BackgroundTasks,
    email: str = Body(),  # required
    password: str = Body(),
    email2: str | None = Body(None),
    name: str = Body(f'user_{generate_random_string()}'),
    allowIp: list[str] = Body(['0.0.0.0/0', '::/0']),
    denyIp: list[str] = Body([]),
    protect: bool = Body(False),
    log: Log_Fields = Depends(log_with_key)
) -> JSONResponse:
    log.req_params = urlencode(
        {'name': name, 'allowIp': ','.join(allowIp), 'denyIp': ','.join(denyIp), 'protect': protect}
    )

    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    system_config = await get_system_config()
    if not system_config.system_allow_register:
        log.res_error = ErrorCode.SYSTEM_REGISTER_FORBIDDEN
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res

    ip_ok = match_ip(log.ip, [system_config.system_allow_ip], [system_config.system_deny_ip])
    if not ip_ok:
        log.res_error = ErrorCode.IP_NOT_ALLOWED
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res

    for _cidr in allowIp + denyIp:
        if not check_cidr(_cidr):
            log.res_error = ErrorCode.OTHER_CIDR_INVALID
            return response_err(background_tasks, log, error_extra=_cidr)

    try:
        aes = Crypto_AES(log.key)
        email = aes.decrypt(email)
        email.decode()
        password = aes.decrypt(password)
        password.decode()
        if email2:
            email2 = aes.decrypt(email2)
            email2.decode()
        else:
            email2 = b''
    except Exception as identifier:
        log.res_error = ErrorCode.ENCRYPTION_INVALID_DATA
        log.internal_error = 'aes.decrypt(email|password|email2)'
        log.debug_error = str(identifier)
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res

    hashed_email = get_hash(email)
    if await email_verify_store.get(hashed_email) != 'register':
        log.res_error = ErrorCode.VERIFY_EMAIL_NOT_FOUND
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res

    session = session_factory()
    try:
        if not await check_password_compliance(session, password.decode()):
            log.res_error = ErrorCode.PASSWORD_WEAK
            return response_err(background_tasks, log)
        user_main = await simple_select_one(session, TB_USERS_MAIN, {'email': hashed_email})
        if user_main:
            log.res_error = ErrorCode.EMAIL_ALREADY_EXISTS
            res = response_err(background_tasks, log)
            await sleep(2 + random() * 2)
            return res
        await email_verify_store.delete(hashed_email)
        await add_user(
            session,
            email,
            password,
            email2=email2,
            name=name,
            allow_ip=allowIp,
            deny_ip=denyIp,
            protect=protect,
            time=log.start_time,
            ip=log.ip
        )
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    return response_200(background_tasks, log)


@router.delete('/user')
async def user_delete(
    background_tasks: BackgroundTasks,
    log: Log_Fields = Depends(log_with_user_info)
) -> JSONResponse:
    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    session = session_factory()
    try:
        if log.user_info['protect']:
            _email = log.user_info['email'] if not log.user_info['email2'] else log.user_info['email2']
            if await email_verify_store.get(_email) != log.user_id.hex:
                log.res_error = ErrorCode.VERIFY_EMAIL_NOT_FOUND
                res = response_err(background_tasks, log)
                await sleep(2 + random() * 2)
                return res
            await email_verify_store.delete(_email)
        if log.user_info['admin']:
            if len(await get_all_users(session, admin=True)) <= 1:
                log.res_error = ErrorCode.SYSTEM_LAST_ADMIN
                return response_err(background_tasks, log)
        await simple_update_one(
            session,
            TB_USERS_MAIN,
            filter={'user_id': log.user_id},
            value={'status': 4}
        )
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    background_tasks.add_task(
        background_run,
        log,
        delete_multi,
        table=TB_USERS_MAIN,
        in_={'user_id': [log.user_id]}
    )
    background_tasks.add_task(
        background_run,
        log,
        insert_history_delete,
        email=log.user_info['email'],
        time_=log.start_time,
        ip=log.ip
    )
    return response_200(background_tasks, log, None)


@router.post('/user/session')
async def user_login(
    background_tasks: BackgroundTasks,
    email: str = Body(),
    password: str = Body(),
    log: Log_Fields = Depends(log_with_key)
) -> JSONResponse:
    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    background_tasks.add_task(
        background_run,
        log,
        delete_expired_users_lock,
        time_=log.start_time
    )

    try:
        aes = Crypto_AES(log.key)
        email = aes.decrypt(email)
        email.decode()
        password = aes.decrypt(password)
        password.decode()
    except Exception as identifier:
        log.res_error = ErrorCode.ENCRYPTION_INVALID_DATA
        log.internal_error = 'aes.decrypt(email|password)'
        log.debug_error = str(identifier)
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res

    nonce = log.key[128:]
    nonce = int.from_bytes(nonce)
    if not await login_nonce_store.get(nonce):
        log.res_error = ErrorCode.NONCE_INVALID
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res
    await login_nonce_store.delete(nonce)

    session = session_factory()
    try:
        user = await get_all_users(session, email_hash=[get_hash(email)])
        if len(user) == 0:
            log.res_error = ErrorCode.EMAIL_NOT_FOUND
            res = response_err(background_tasks, log)
            await sleep(2 + random() * 2)
            return res
        user = user[0]
        log.user_id = user['user_id']
        system_config = await get_system_config()
        if not match_ip(
            log.ip,
            [user['allow_ip'], system_config.system_allow_ip],
            [user['deny_ip'], system_config.system_deny_ip]
        ):
            log.res_error = ErrorCode.IP_FORBIDDEN
            background_tasks.add_task(
                background_run,
                log,
                insert_history_login,
                user_id=user['user_id'],
                time_=log.start_time,
                ip=log.ip,
                result=4
            )
            res = response_err(background_tasks, log)
            await sleep(2 + random() * 2)
            return res

        if system_config.user_lock_ip_only:
            lock = await select_multi(
                session,
                TB_USERS_LOCK,
                ['time'],
                in_={'user_id': [user['user_id']], 'ip': [log.ip]},
                gt={'time': log.start_time - system_config.user_lock_period * 60}
            )
        else:
            lock = await select_multi(
                session,
                TB_USERS_LOCK,
                ['time'],
                in_={'user_id': [user['user_id']]},
                gt={'time': log.start_time - system_config.user_lock_period * 60}
            )
        if len(lock) > 0:
            log.res_error = ErrorCode.USER_LOCKED
            await insert_history_login(session, user['user_id'], log.start_time, log.ip, 3)
            await session.commit()
            res = response_err(background_tasks, log, error_extra=lock[0]['time'])
            await sleep(2 + random() * 2)
            return res

        if get_hash(password, user['password_salt']) == user['password']:
            background_tasks.add_task(
                background_update_user_status,
                log,
                user['user_id'],
                password.decode()
            )
            background_tasks.add_task(
                background_run,
                log,
                insert_history_login,
                user_id=user['user_id'],
                time_=log.start_time,
                ip=log.ip,
                result=0
            )
            background_tasks.add_task(
                background_run,
                log,
                delete_multi,
                table=TB_USERS_LOGIN_FAIL,
                in_={'user_id': [user['user_id']], 'ip': [log.ip]}
            )
            session_token = urandom(64)
            hashed_session_token = get_hash(session_token, algorithm='md5')
            session_id, = await simple_insert_one(
                session,
                TB_USERS_SESSION,
                {
                    'session_token': [hashed_session_token],
                    'user_id': user['user_id'],
                    'ip': log.ip,
                    'login_time': log.start_time,
                    'refresh_time': log.start_time
                },
                returning=['session_id']
            )
            log.session_id = session_id
            log.session_tokens = [hashed_session_token]
            await session.commit()
            session_nonce_store[session_id] = [nonce]
        else:
            count = await select_multi(
                session,
                TB_USERS_LOGIN_FAIL,
                ['count'],
                in_={'user_id': [user['user_id']], 'ip': [log.ip]},
                gt={'time': log.start_time - system_config.login_fail_count_reset_period * 60}
            )
            if len(count) == 0:
                count = 1
            else:
                count = count[0]['count']
                count += 1
            if count >= system_config.login_fail_lock_number:
                await simple_upsert_one(
                    session,
                    TB_USERS_LOCK,
                    {'user_id': user['user_id'], 'ip': log.ip, 'time': log.start_time},
                    ['user_id', 'ip']
                )
                await delete_multi(
                    session,
                    TB_USERS_LOGIN_FAIL,
                    in_={'user_id': [user['user_id']], 'ip': [log.ip]}
                )
                await insert_history_login(session, user['user_id'], log.start_time, log.ip, 2)
                await insert_history_config(session, user['user_id'], log.start_time, log.ip, locked=True)
                await session.commit()
                log.res_error = ErrorCode.USER_LOCKED
                res = response_err(background_tasks, log, error_extra=log.start_time)
                await sleep(2 + random() * 2)
                return res
            else:
                await simple_upsert_one(
                    session,
                    TB_USERS_LOGIN_FAIL,
                    {'user_id': user['user_id'], 'ip': log.ip, 'count': count, 'time': log.start_time},
                    ['user_id', 'ip']
                )
                await insert_history_login(session, user['user_id'], log.start_time, log.ip, 1)
                await session.commit()
                log.res_error = ErrorCode.PASSWORD_WRONG
                res = response_err(
                    background_tasks,
                    log,
                    error_extra=system_config.login_fail_lock_number - count
                )
                await sleep(2 + random() * 2)
                return res
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    return response_200(
        background_tasks,
        log,
        True,
        session_token
    )


# 只在用户登录时调用，降低开销，保护用户体验
async def background_update_user_status(log: Log_Fields, user_id: UUID, password: str) -> None:
    session = session_factory()
    try:
        system_config = await get_system_config()
        _time = await select_multi(
            session,
            TB_USERS_HISTORY_PASSWD,
            ['time'],
            in_={'user_id': [user_id]},
            order={'id': True},
            limit=1
        )
        new_status = 0
        if (
            system_config.user_passwd_expire > 0
            and _time[0]['time'] < time() - system_config.user_passwd_expire * 24 * 60 * 60
        ):
            new_status = 1
        if not await check_password_compliance(session, password):
            new_status = 2 | new_status
        await simple_update_one(
            session,
            TB_USERS_MAIN,
            filter={'user_id': user_id},
            value={'status': new_status}
        )
        await session.commit()
    except Exception as identifier:
        await default_logger.log('ERROR', log, server_error='background_update_user_status')
        await default_logger.log('DEBUG', log, server_error=str(identifier))
    await session.close()


@router.delete('/user/session')
async def user_logout(
    background_tasks: BackgroundTasks,
    target_session_id: str | None = None,
    log: Log_Fields = Depends(log_with_user_info)
) -> JSONResponse:
    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    try:
        if target_session_id:
            target_session_id = UUID(target_session_id)
    except Exception:
        log.res_error = ErrorCode.SESSION_MALFORMED_ID
        log.internal_error = 'UUID(target_session_id)'
        res = response_err(background_tasks, log)
        await sleep(2 + random() * 2)
        return res

    session = session_factory()
    try:
        if target_session_id:
            target_session = await simple_select_one(
                session,
                TB_USERS_SESSION,
                {'session_id': target_session_id, 'user_id': log.user_id}
            )
            if not target_session:
                log.res_error = ErrorCode.SESSION_NOT_FOUND
                return response_err(background_tasks, log)
            await delete_multi(
                session,
                TB_USERS_SESSION,
                in_={'session_id': [target_session_id]}
            )
            new_session_token, time_ = await update_user_session(
                session,
                log.session_id,
                log.session_tokens,
                log.ip,
                log.start_time
            )
            if log.start_time != time_:
                new_session_token = b''
        else:
            await delete_multi(
                session,
                TB_USERS_SESSION,
                in_={'session_id': [log.session_id]}
            )
            new_session_token = b''
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    return response_200(
        background_tasks,
        log,
        False if target_session_id and target_session_id != log.session_id else None,
        new_session_token
    )


@router.get('/user/sessions')
async def user_list_sessions(
    background_tasks: BackgroundTasks,
    log: Log_Fields = Depends(log_with_user_info)
) -> JSONResponse:
    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    session = session_factory()
    try:
        system_config = await get_system_config()
        data = await select_multi(
            session,
            TB_USERS_SESSION,
            ['session_id', 'ip', 'login_time'],
            in_={'user_id': [log.user_id]}, order={'id': True},
            limit=system_config.user_session_number
        )
        for _r in data:
            _r['session_id'] = _r['session_id'].hex
        new_session_token, time_ = await update_user_session(
            session,
            log.session_id,
            log.session_tokens,
            log.ip,
            log.start_time
        )
        if log.start_time != time_:
            new_session_token = b''
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    return response_200(
        background_tasks,
        log,
        session_token=new_session_token,
        data=data
    )


@router.get('/user/config')
async def user_get_config(
    background_tasks: BackgroundTasks,
    log: Log_Fields = Depends(log_with_user_info)
) -> JSONResponse:
    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    session = session_factory()
    try:
        data = {
            'email_mask': log.user_info['email_mask'],
            'email2_mask': log.user_info['email2_mask'],
            'name': log.user_info['name'],
            'allow_ip': log.user_info['allow_ip'],
            'deny_ip': log.user_info['deny_ip'],
            'protect': log.user_info['protect']
        }
        new_session_token, time_ = await update_user_session(
            session,
            log.session_id,
            log.session_tokens,
            log.ip,
            log.start_time
        )
        if log.start_time != time_:
            new_session_token = b''
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    return response_200(
        background_tasks,
        log,
        session_token=new_session_token,
        data=data
    )


@router.put('/user/config')
async def user_update_config(
    background_tasks: BackgroundTasks,
    name: str | None = Body(None),
    allowIp: list[str] | None = Body(None),
    denyIp: list[str] | None = Body(None),
    protect: bool | None = Body(None),
    log: Log_Fields = Depends(log_with_user_info)
) -> JSONResponse:
    log.req_params = urlencode({
        'name': name,
        'allowIp': ','.join(allowIp) if allowIp else None,
        'denyIp': ','.join(denyIp) if denyIp else None,
        'protect': protect
    })
    if log.res_error != ErrorCode.OK:
        res = response_err(background_tasks, log)
        if log.res_error.http_status in (400, 403):
            await sleep(2 + random() * 2)
        return res

    insert_data = {}
    if name:
        insert_data['name'] = name
    if allowIp:
        for _cidr in allowIp:
            if not check_cidr(_cidr):
                log.res_error = ErrorCode.OTHER_CIDR_INVALID
                return response_err(background_tasks, log, error_extra=_cidr)
        insert_data['allow_ip'] = allowIp
    if denyIp:
        for _cidr in denyIp:
            if not check_cidr(_cidr):
                log.res_error = ErrorCode.OTHER_CIDR_INVALID
                return response_err(background_tasks, log, error_extra=_cidr)
        insert_data['deny_ip'] = denyIp
    if protect is not None:
        insert_data['protect'] = protect
    if not insert_data:
        log.res_error = ErrorCode.OTHER_PARAMETER_REQUIRED
        return response_err(background_tasks, log)

    session = session_factory()
    try:
        if log.user_info['protect']:
            _email = log.user_info['email'] if not log.user_info['email2'] else log.user_info['email2']
            if await email_verify_store.get(_email) != log.user_id.hex:
                log.res_error = ErrorCode.VERIFY_EMAIL_NOT_FOUND
                res = response_err(background_tasks, log)
                await sleep(2 + random() * 2)
                return res
            await email_verify_store.delete(_email)
        await simple_update_one(
            session,
            TB_USERS_OTHER,
            filter={'user_id': log.user_id},
            value=insert_data
        )
        await insert_history_config(
            session,
            log.user_id,
            log.start_time,
            log.ip,
            **insert_data
        )
        new_session_token, time_ = await update_user_session(
            session,
            log.session_id,
            log.session_tokens,
            log.ip,
            log.start_time
        )
        if log.start_time != time_:
            new_session_token = b''
        await session.commit()
    except Exception as identifier:
        log.res_error = ErrorCode.SERVER_INTERNAL_ERROR
        log.debug_error = str(identifier)
        return response_err(background_tasks, log)
    finally:
        await session.close()

    return response_200(background_tasks, log, session_token=new_session_token)
