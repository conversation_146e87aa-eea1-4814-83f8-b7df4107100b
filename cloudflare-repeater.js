export default {
  async email(message, env, ctx) {
    const from = message.from
    const to = message.to
    const signature = message.headers.get('x-signature');
    const url = new URL('https://data_midware/api/email');
    url.searchParams.set('from', from);
    url.searchParams.set('to', to);
    url.searchParams.set('signature', signature);
    await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
